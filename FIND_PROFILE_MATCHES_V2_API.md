# NEW v2 API Documentation

## Overview

The NEW v2 API implements the complete matching logic as specified in `NEW_MATCHING_LOGIC.md`. This includes two endpoints that work together:

1. **`create-synthetic-profile-v2`** - Creates synthetic profile from user profile
2. **`find-profile-matches-v2`** - Finds matches using new weights and logic

## Endpoints

### 1. Create Synthetic Profile v2
**POST** `/create-synthetic-profile-v2`

### 2. Find Profile Matches v2
**POST** `/find-profile-matches-v2`

## New Matching Algorithm v2

### Matching Formula
```
Matching score = Face_embedding*w1 + Personality*w2 + Interest*w3 + Value*w4 + Race*w5
```

### Updated Weights
- **Face embedding (w1)**: 50% (reduced from 60% in v1)
- **Personality (w2)**: 20% (increased from 10% in v1)  
- **Interests (w3)**: 10% (reduced from 20% in v1)
- **Values (w4)**: 10% (NEW in v2)
- **Race (w5)**: 10% (unchanged)

### Key Changes from v1
1. **Reduced face embedding importance** from 60% to 50%
2. **Increased personality importance** from 10% to 20%
3. **Reduced interest importance** from 20% to 10%
4. **Added values matching** (traditionalism + conformity): 10%
5. **Uses new data source**: `script/new_data.csv` instead of missing CSV file
6. **Updated embedding file**: `files/dating_app_2k_embedding_s512.pickle`

## Data Sources

### CSV Data
- **Primary**: `script/new_data.csv` (2,667 profiles with complete data)
- **Fallback**: `files/dating_app_sample_100_essay_tag.csv` (for backward compatibility)

### Face Embeddings
- **File**: `files/dating_app_2k_embedding_s512.pickle`
- **Dimensions**: 512-dimensional embeddings from InsightFace

## NEW v2 Synthetic Profile Creation Logic

### 1. Personality (20% weight)
- **Input**: User's 5 personality traits (openness, conscientiousness, extraversion, agreeableness, neuroticism)
- **Logic**: Randomly pick 3 traits from user's 5 traits
- **Output**: 3 personality traits for synthetic profile

### 2. Interests (10% weight)
- **Input**: User's interests (any number)
- **Logic**: Generate 3 new interests from same categories using `files/category_to_interests.json`
- **Output**: 3 new interests in same categories as user's interests
- **Matching**: Text embeddings with cosine similarity (Gemini-based - placeholder implemented)

### 3. Values (10% weight)
- **Input**: User's traditionalism and conformity values
- **Logic**: Copy exactly from user profile
- **Output**: Same values as user

### 4. Face Embedding (50% weight)
- **Input**: User's image URL
- **Logic**: Use same image for synthetic profile
- **Output**: Face embedding for matching

### 5. Race (10% weight)
- **Input**: User's race
- **Logic**: Keep same as user
- **Output**: Same race as user

## API Usage Flow

### Step 1: Create Synthetic Profile v2

**POST** `/create-synthetic-profile-v2`

#### Request Body
```json
{
  "first_name": "Alex",
  "last_name": "Chen",
  "gender": "m",
  "age": 28,
  "race": "asian",
  "interests": "Photography, Board Games, Running",
  "openness": "Imaginative",
  "conscientiousness": "Organised",
  "extraversion": "Outgoing",
  "agreeableness": "Warm",
  "neuroticism": "Calm",
  "traditionalism": "Progressive",
  "conformity": "Autonomous"
}
```

#### Response
```json
{
  "success": true,
  "message": "Synthetic profile v2 created successfully",
  "data": {
    "synthetic_profile": {
      "first_name": "Alex",
      "gender": "m",
      "age": 28,
      "race": "asian",
      "interests": "Digital Art, Chess, Swimming",
      "personality_tags": "Imaginative, Outgoing, Warm",
      "traditionalism": "Progressive",
      "conformity": "Autonomous"
    },
    "original_interests": ["Photography", "Board Games", "Running"],
    "synthetic_interests": ["Digital Art", "Chess", "Swimming"],
    "original_personality": ["Imaginative", "Organised", "Outgoing", "Warm", "Calm"],
    "selected_personality": ["Imaginative", "Outgoing", "Warm"]
  },
  "creation_time_seconds": 0.05
}
```

### Step 2: Find Matches with Synthetic Profile

**POST** `/find-profile-matches-v2`

#### Request Body
```json
{
  "synthetic_profile": {
    // Use synthetic_profile from Step 1 response
  },
  "image_url": "https://example.com/image.jpg",
  "include_explanations": true
}
```

## Response Format

```json
{
  "success": true,
  "message": "Profile matches found successfully using NEW matching logic v2",
  "data": {
    "matches": [
      {
        "id": 12345,
        "first_name": "Jennifer",
        "age": 26,
        "interests": "Photography, Travel, Yoga",
        "same_interests": 1,
        "same_personality": 3,
        "cosine_score": 0.75,
        "values_score": 2,
        "same_race": 1,
        "weighted_average": 0.82,
        "explanation": "Both you and Jennifer share a love for photography and have compatible personalities..."
      }
    ],
    "total_candidates": 1250,
    "all_matches": [...],
    "matching_algorithm": "v2",
    "weights": {
      "face_embedding": 0.5,
      "personality": 0.2,
      "interests": 0.1,
      "values": 0.1,
      "race": 0.1
    }
  },
  "matching_time_seconds": 2.45
}
```

## Matching Components

### 1. Face Embedding (50% weight)
- Uses InsightFace 512-dimensional embeddings
- Cosine similarity calculation
- Normalized to 0-1 range

### 2. Personality Matching (20% weight)
- Compares Big 5 personality traits
- Supports both individual trait fields and personality_tags
- Counts overlapping traits (0-5)

### 3. Interest Matching (10% weight)
- Text-based interest comparison
- Counts overlapping interests (0-5)
- Normalized to 0-1 range

### 4. Values Matching (10% weight - NEW)
- **Traditionalism**: Traditional vs Progressive
- **Conformity**: Compliant vs Autonomous
- Counts matching values (0-2)
- Normalized to 0-1 range

### 5. Race Matching (10% weight)
- Binary match (1 if same race, 0 if different)

## Error Handling

### 400 Bad Request
- Missing required fields (traditionalism, conformity)
- Invalid synthetic profile data
- Missing gender or image_url

### 500 Internal Server Error
- Data loading failures
- Face embedding processing errors
- AI explanation generation failures

## Testing

Use the provided test script:
```bash
python test_find_profile_matches_v2.py
```

## Performance

- **Expected response time**: 2-15 seconds (depending on explanations)
- **Data caching**: CSV and embedding data cached for performance
- **Explanation generation**: Optional (set `include_explanations: false` for faster responses)

## Backward Compatibility

- v1 endpoint (`/find-profile-matches`) remains unchanged
- Both endpoints can be used simultaneously
- v2 requires additional fields (traditionalism, conformity)

## Example Usage

### Python
```python
import requests

profile_data = {
    "synthetic_profile": {
        "first_name": "Alex",
        "gender": "m",
        "age": 28,
        "interests": "Photography, Hiking, Cooking",
        "personality_tags": "Imaginative, Organised, Outgoing, Warm, Calm",
        "traditionalism": "Progressive",
        "conformity": "Autonomous",
        "race": "asian"
    },
    "image_url": "https://example.com/image.jpg",
    "include_explanations": True
}

response = requests.post(
    "http://localhost:8000/find-profile-matches-v2",
    json=profile_data,
    timeout=60
)

if response.status_code == 200:
    result = response.json()
    matches = result["data"]["matches"]
    print(f"Found {len(matches)} matches using v2 algorithm")
```

### cURL
```bash
curl -X POST "http://localhost:8000/find-profile-matches-v2" \
  -H "Content-Type: application/json" \
  -d '{
    "synthetic_profile": {
      "gender": "m",
      "interests": "Photography, Hiking",
      "traditionalism": "Progressive",
      "conformity": "Autonomous"
    },
    "image_url": "https://example.com/image.jpg",
    "include_explanations": false
  }'
```
