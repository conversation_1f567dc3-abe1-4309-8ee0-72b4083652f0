# Controller Service

This is the FastAPI controller service that provides the v2 matching API endpoints.

## Services

- **Synthetic Profile Creation v2**: `/create-synthetic-profile-v2`
- **Profile Matching v2**: `/find-profile-matches-v2`
- **ComfyUI Workflow Execution**: `/execute-workflow`
- **Personality Tags Generation**: `/generate-personality-tags`
- **Cache Management**: `/refresh-profiles-cache`

## Environment Variables

Required environment variables (set in `.env` file):

```bash
GEMINI_API_KEY=your_gemini_api_key
GOOGLE_APPLICATION_CREDENTIALS=/app/gcp-credentials.json
BUCKET_NAME=comfyui-data-analytic-project-424703
FOLDER_PATH=comfyui/input
DIRECTUS_BASE_URL=http://directus:8055
DIRECTUS_ADMIN_TOKEN=your_directus_admin_token
```

## Docker Deployment

The service is deployed as part of the CMS docker-compose stack:

```bash
cd cms
docker-compose up -d controller
```

## Access

- **Internal**: `http://controller:8000` (within Docker network)
- **External**: `http://controller.copula.site` (via nginx proxy)

## Health Check

- **Endpoint**: `/health`
- **Docker Health Check**: Enabled with 30s intervals

## Files Structure

```
controller/
├── main.py                     # Main FastAPI application
├── match_explanation_engine.py # AI explanation engine
├── gcp_storage.py             # Google Cloud Storage client
├── requirements.txt           # Python dependencies
├── Dockerfile                 # Docker build configuration
├── gcp-credentials.json       # GCP service account key
├── workflow.json              # ComfyUI workflow configuration
└── files/                     # Data files
    ├── category_to_interests.json
    ├── dating_app_2k_embedding_s512.pickle
    └── new_data.csv
```

## API Documentation

Once running, visit:
- `http://controller.copula.site/docs` - Interactive API documentation
- `http://controller.copula.site/redoc` - ReDoc documentation
