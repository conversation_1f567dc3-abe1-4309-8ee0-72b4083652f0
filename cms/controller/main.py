import os
import logging
from typing import Optional
from functools import lru_cache
from fastapi import FastAPI, File, UploadFile, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import magic
from PIL import Image
import io
import base64
import json
import pathlib
import time
import requests
import random
import pickle
import csv
from datetime import datetime, timedelta
import threading
import uuid
import google.generativeai as genai
from dotenv import load_dotenv
import tempfile
import urllib.parse
import cv2
import numpy as np
from insightface.app import FaceAnalysis

from gcp_storage import GCPStorageClient

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Image Upload API",
    description="API for uploading images to Google Cloud Storage",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
BUCKET_NAME = "comfyui-data-analytic-project-424703"
FOLDER_PATH = "comfyui/input"
CREDENTIALS_PATH = "gcp-credentials.json"

# Initialize InsightFace app globally
face_app = None
try:
    face_app = FaceAnalysis(name="buffalo_l", providers=['CPUExecutionProvider'])
    # face_app.prepare(ctx_id=0, det_size=(640, 640))
    face_app.prepare(ctx_id=0, det_thresh=0.5, det_size=(512, 512))
    logger.info("InsightFace app initialized successfully")
except Exception as e:
    logger.warning(f"Failed to initialize InsightFace app: {str(e)}. Will use fallback embedding method.")
    face_app = None

# Supported image formats
SUPPORTED_IMAGE_TYPES = {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'image/webp': ['.webp'],
    'image/bmp': ['.bmp'],
    'image/tiff': ['.tiff', '.tif']
}

# Maximum file size (20MB)
MAX_FILE_SIZE = 20 * 1024 * 1024

# Pydantic models


class WorkflowRequest(BaseModel):
    prompt: str  # Description of how to generate the image
    image_url: str  # URL of the input image
    weight: Optional[float] = 0.7  # Weight parameter for PuLID end_at and safety threshold (0.0-1.0)
    cloudrun_url: Optional[str] = "https://comfyui-cloudrun-435103809426.asia-southeast1.run.app"  # ComfyUI Cloud Run URL
    output_node: Optional[str] = "51"  # Output node ID (default to SaveImage node)



class ProfileMatchingRequest(BaseModel):
    # Synthetic profile data for matching
    synthetic_profile: dict  # The synthetic profile data
    image_url: str  # URL of the image for face embedding
    include_explanations: bool = True  # Whether to generate AI explanations (slower)

class ProfileResponse(BaseModel):
    # Response model for profile data from CSV
    id: int
    file_name: str
    first_name: str
    last_name: str
    height_cm: float
    age: int
    status: str
    gender: str
    orientation: str
    body_type: str
    diet: str
    drinks: str
    smokes: str
    education: str
    job: str
    location: str
    family_plan: str
    pets: str
    desired_relationship: str
    openness: float
    conscientiousness: float
    extraversion: float
    agreeableness: float
    neuroticism: float
    interests: str
    essay0: str
    personality_tags: str

class PersonalityTagsRequest(BaseModel):
    # Request model for personality tags generation
    openness: float
    conscientiousness: float
    extraversion: float
    agreeableness: float

class GeminiImageGenerationRequest(BaseModel):
    # Request model for Gemini image generation
    image_url: str  # URL of the source image
    prompt: Optional[str] = None  # Custom prompt (uses default if not provided)

class SyntheticProfileMatchingRequest(BaseModel):
    # Complete profile information matching CSV structure (kept for backward compatibility)
    file_name: str  # Image path or URL for face embedding
    first_name: str
    last_name: str
    height_cm: float
    age: int
    status: str
    gender: str  # 'm' or 'f'
    orientation: str
    body_type: str
    diet: str
    drinks: str
    smokes: str
    education: str
    job: str
    location: str
    family_plan: str
    pets: str
    desired_relationship: str

    # Personality scores (0-10)
    openness: float
    conscientiousness: float
    extraversion: float
    agreeableness: float
    neuroticism: float

    # Interests (comma-separated string or list)
    interests: str  # Will be processed as comma-separated string like in CSV

    # Essay and personality tags
    essay0: str
    personality_tags: str  # Comma-separated string like in CSV

# Initialize GCP Storage client
try:
    storage_client = GCPStorageClient(
        bucket_name=BUCKET_NAME,
        credentials_path=CREDENTIALS_PATH
    )
    logger.info("GCP Storage client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize GCP Storage client: {str(e)}")
    storage_client = None





def execute_workflow_api(image_url: str, input_prompt: str, weight: float, cloudrun_url: str, output_node: str) -> dict:
    """
    Execute ComfyUI workflow with given parameters.

    Args:
        image_url: URL of the input image
        input_prompt: Text prompt for image generation
        weight: Weight parameter for PuLID end_at and safety threshold (0.0-1.0)
        cloudrun_url: ComfyUI Cloud Run service URL
        output_node: Output node ID to retrieve results from

    Returns:
        Dictionary containing output URL and execution metadata
    """
    try:
        # Load workflow template
        workflow_path = pathlib.Path(__file__).parent / "workflow.json"

        if not workflow_path.exists():
            raise FileNotFoundError(f"Workflow file not found: {workflow_path}")

        with open(workflow_path) as f:
            workflow = json.load(f)

        # Update workflow with input parameters
        # Node 22 is the positive prompt encoder
        enhanced_prompt = input_prompt + " blur street background, detailed skin, realistic skin texture, dramatic, cinematic, dof, 8k uhd, dslr, high quality"
        workflow["22"]["inputs"]["text"] = enhanced_prompt

        # Node 12 is the image loader
        workflow["12"]["inputs"]["image"] = image_url.split("/")[-1]

        # Node 33 is the PuLID application - set end_at weight
        workflow["33"]["inputs"]["weight"] = weight


        logger.info(f"Executing workflow with prompt: {enhanced_prompt}")
        logger.info(f"Using image URL: {image_url}")
        logger.info(f"Using weight: {weight}")

        # Prepare request payload
        prompt_payload = {"prompt": workflow}

        # Execute workflow
        start_time = time.time()
        response = requests.post(f"{cloudrun_url}/prompt", json=prompt_payload, timeout=1800)  # 30 minute timeout
        execution_time = time.time() - start_time

        response.raise_for_status()

        # Parse response
        result = response.json()
        logger.info(f"Workflow response: {result}")

        # Extract output
        output_node = "51"

        output = result.get("outputs", {}).get(output_node, None)
        print(f"Output for node '{output_node}': {output}")

        filename = output["images"][0]["filename"]
        output_url = f"{cloudrun_url}/view?filename={filename}"

        return {
            "success": True,
            "output_url": output_url,
            "execution_time_seconds": round(execution_time, 2),
            "filename": filename,
            "prompt_used": enhanced_prompt,
            "input_image_url": image_url,
            "weight_used": weight,
            "output_node": output_node
        }

    except requests.exceptions.Timeout:
        raise HTTPException(
            status_code=status.HTTP_408_REQUEST_TIMEOUT,
            detail="Workflow execution timed out (5 minutes)"
        )
    except requests.exceptions.RequestException as e:
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"Error communicating with ComfyUI service: {str(e)}"
        )
    except FileNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error in workflow execution: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Workflow execution failed: {str(e)}"
        )



def find_profile_matches_v2(synthetic_profile: dict, image_url: str, include_explanations: bool = True) -> dict:
    """
    Find matches for a synthetic profile using NEW matching logic v2

    New weights:
    - Face embedding: 50% (w1)
    - Personality: 20% (w2)
    - Interests: 10% (w3)
    - Values (traditionalism + conformity): 10% (w4)
    - Race: 10% (w5)

    Args:
        synthetic_profile: The synthetic profile data
        image_url: URL of the image for face embedding
        include_explanations: Whether to generate AI explanations (slower)

    Returns:
        Dictionary containing match results and explanations
    """
    try:
        # Use the new perform_profile_matching_v2 function
        matches_result = perform_profile_matching_v2(synthetic_profile, image_url, include_explanations)

        return {
            'matches': matches_result,
            'total_candidates': len(matches_result.get('matches', [])),
            'top_5_with_explanations': matches_result.get('top_5_with_explanations', []),
            'match_df': matches_result.get('matches', [])[:50]  # Return top 50 matches
        }

    except Exception as e:
        logger.error(f"Error finding profile matches v2: {str(e)}")
        raise


def create_synthetic_profile_v2(user_profile: dict) -> dict:
    """
    Create synthetic profile using NEW logic v2 from user profile

    NEW Logic:
    1. Personality: Pick 3 random traits from user's 5 personality traits
    2. Interests: Generate 3 new interests from same categories as user's interests
    3. Values: Copy user's traditionalism and conformity values
    4. Race: Keep same as user
    5. Gender: Make opposite gender (m -> f, f -> m)
    6. Height: Adjust based on gender (if male->female: -1 to -10cm, if female->male: +1 to +10cm)

    Args:
        user_profile: Original user profile dictionary

    Returns:
        Dictionary containing synthetic profile
    """
    try:
        # Load category to interests mapping
        category_file = pathlib.Path(__file__).parent / "files" / "category_to_interests.json"
        with open(category_file, 'r', encoding='utf-8') as f:
            category_to_interests = json.load(f)

        # Create synthetic profile base
        synthetic_profile = user_profile.copy()

        # 1. Personality: Pick 3 random traits from user's 5 personality traits
        personality_fields = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism']
        user_personality_traits = []

        for field in personality_fields:
            if field in user_profile and user_profile[field]:
                user_personality_traits.append(user_profile[field])

        # Randomly select 3 traits from the 5
        if len(user_personality_traits) >= 3:
            selected_traits = random.sample(user_personality_traits, 3)
        else:
            selected_traits = user_personality_traits  # Use all if less than 3

        synthetic_profile['personality_tags'] = ', '.join(selected_traits)

        # 2. Interests: Generate 3 new interests from same categories
        user_interests = user_profile.get('interests', '').split(', ') if isinstance(user_profile.get('interests'), str) else user_profile.get('interests', [])

        # Find categories for user interests
        user_categories = []
        for interest in user_interests:
            interest = interest.strip()
            for category, interests_list in category_to_interests.items():
                if interest in interests_list:
                    user_categories.append(category)
                    break

        # Generate 3 new interests from same categories
        synthetic_interests = []
        for i in range(min(3, len(user_categories))):
            category = user_categories[i]
            available_interests = category_to_interests[category]
            # Pick a different interest from the same category
            category_interests = [interest for interest in available_interests if interest not in user_interests]
            if category_interests:
                synthetic_interests.append(random.choice(category_interests))
            else:
                # If no other interests in category, use original
                synthetic_interests.append(user_interests[i] if i < len(user_interests) else available_interests[0])

        # Fill up to 3 interests if needed
        while len(synthetic_interests) < 3 and len(user_interests) > len(synthetic_interests):
            synthetic_interests.append(user_interests[len(synthetic_interests)])

        synthetic_profile['interests'] = ', '.join(synthetic_interests)

        # 3. Values: Copy user's traditionalism and conformity values
        synthetic_profile['traditionalism'] = user_profile.get('traditionalism', '')
        synthetic_profile['conformity'] = user_profile.get('conformity', '')

        # 4. Race: Keep same as user
        synthetic_profile['race'] = user_profile.get('race', '')

        # 5. Gender: Make opposite gender
        original_gender = user_profile.get('gender', '')
        if original_gender.lower() == 'm':
            synthetic_profile['gender'] = 'f'
        elif original_gender.lower() == 'f':
            synthetic_profile['gender'] = 'm'
        else:
            synthetic_profile['gender'] = original_gender  # Keep original if not m/f

        # 6. Height: Adjust based on gender difference
        original_height = user_profile.get('height', 0)
        if isinstance(original_height, (int, float)) and original_height > 0:
            height_diff = random.randint(1, 10)  # Random 1-10 cm difference

            if original_gender.lower() == 'm' and synthetic_profile['gender'] == 'f':
                # Original is male, synthetic is female -> make her shorter
                synthetic_profile['height'] = original_height - height_diff
            elif original_gender.lower() == 'f' and synthetic_profile['gender'] == 'm':
                # Original is female, synthetic is male -> make him taller
                synthetic_profile['height'] = original_height + height_diff
            else:
                # Keep original height if gender logic doesn't apply
                synthetic_profile['height'] = original_height
        else:
            # Keep original height if no valid height data
            synthetic_profile['height'] = original_height

        logger.info(f"Created synthetic profile v2 with interests: {synthetic_interests}")
        logger.info(f"Selected personality traits: {selected_traits}")

        return {
            'synthetic_profile': synthetic_profile,
            'original_interests': user_interests,
            'synthetic_interests': synthetic_interests,
            'original_personality': user_personality_traits,
            'selected_personality': selected_traits
        }

    except Exception as e:
        logger.error(f"Error creating synthetic profile v2: {str(e)}")
        raise


def generate_image_with_gemini(image_url: str, prompt: str = None) -> dict:
    """
    Generate image using Google Gemini with source image URL and prompt

    Args:
        image_url: URL of the source image
        prompt: Text prompt for image generation (uses default if None)

    Returns:
        Dictionary containing generated image data and metadata
    """
    start_time = time.time()

    # Default prompt if none provided (define outside try block for scope)
    prompt_used = prompt if prompt else (
        "High quality photo of a person, professional attire, natural smile, "
        "half body portrait, blur background, detailed skin texture. "
        "Keep the face unchanged but enhance the overall image quality."
    )

    try:
        import google.generativeai as genai
        from google.generativeai import GenerativeModel

        # Configure Gemini API
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if not gemini_api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")

        genai.configure(api_key=gemini_api_key)

        logger.info(f"Generating image with Gemini using prompt: {prompt_used[:100]}...")
        logger.info(f"Source image URL: {image_url}")

        # Initialize Gemini model
        model = GenerativeModel("gemini-2.5-flash-image-preview")

        # Download image data for Gemini (Gemini needs image data, not URL)
        import requests
        response_img = requests.get(image_url, timeout=30)
        response_img.raise_for_status()
        image_data = response_img.content

        # Generate content with prompt and image data
        response = model.generate_content(
            contents=[prompt_used, {"mime_type": "image/jpeg", "data": image_data}],
        )

        # Extract image data from response
        image_parts = [
            part.inline_data.data
            for part in response.candidates[0].content.parts
            if part.inline_data
        ]

        if not image_parts:
            raise ValueError("No image data found in Gemini response")

        # Get the generated image data
        generated_image_data = image_parts[0]

        # Upload to Google Cloud Storage
        if not storage_client:
            raise ValueError("Google Cloud Storage client not available")

        # Generate unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        filename = f"gemini_generated_{timestamp}_{unique_id}.png"

        # Upload to GCS bucket
        bucket_name = "comfyui-data-analytic-project-424703"
        blob_path = f"comfyui/common/{filename}"

        try:
            # Use the storage_client's upload_file_from_memory method
            from io import BytesIO
            file_obj = BytesIO(generated_image_data)
            public_url = storage_client.upload_file_from_memory(
                file_obj=file_obj,
                destination_blob_name=blob_path,
                content_type="image/png",
                make_public=True
            )



            logger.info(f"Successfully uploaded generated image to GCS: {public_url}")

            return {
                "success": True,
                "message": "Image generated successfully with Gemini",
                "data": {
                    "generated_image_url": public_url,
                    "filename": filename,
                    "bucket_path": blob_path,
                    "prompt_used": prompt_used,
                    "source_image_url": image_url
                },
                "generation_time_seconds": round(time.time() - start_time, 3)
            }

        except Exception as upload_error:
            logger.error(f"Error uploading to GCS: {str(upload_error)}")
            # Fallback to base64 if upload fails
            image_base64 = base64.b64encode(generated_image_data).decode('utf-8')
            return {
                "success": True,
                "message": "Image generated successfully with Gemini",
                "data": {
                    "generated_image_url": None,
                    "generated_image_base64": f"data:image/png;base64,{image_base64}",
                    "filename": None,
                    "bucket_path": None,
                    "upload_error": str(upload_error),
                    "prompt_used": prompt_used,
                    "source_image_url": image_url
                },
                "generation_time_seconds": round(time.time() - start_time, 3)
            }

    except Exception as e:
        logger.error(f"Error generating image with Gemini: {str(e)}")
        return {
            "success": False,
            "message": f"Failed to generate image: {str(e)}",
            "data": {
                "generated_image_url": None,
                "generated_image_base64": None,
                "filename": None,
                "bucket_path": None,
                "prompt_used": prompt_used,
                "source_image_url": image_url
            },
            "generation_time_seconds": round(time.time() - start_time, 3)
        }





def download_image_from_url(url: str) -> str:
    """
    Download image from URL to a temporary file.

    Args:
        url: URL of the image to download

    Returns:
        Path to the downloaded temporary file
    """
    try:
        logger.info(f"Downloading image from URL: {url}")

        # Create a temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
        temp_path = temp_file.name
        temp_file.close()

        # Download the image
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()

        # Save to temporary file
        with open(temp_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

        logger.info(f"Image downloaded successfully to: {temp_path}")
        return temp_path

    except Exception as e:
        logger.error(f"Error downloading image from URL {url}: {str(e)}")
        raise


def get_face_embedding(image_path_or_url: str) -> list:
    """
    Extract face embedding from image using InsightFace (compatible with database method).

    Args:
        image_path_or_url: Path to the image file or URL

    Returns:
        Face embedding array (512-dimensional from InsightFace)
    """
    temp_file_path = None
    try:
        # Check if it's a URL or local path
        if image_path_or_url.startswith(('http://', 'https://')):
            # Download image from URL
            temp_file_path = download_image_from_url(image_path_or_url)
            image_path = temp_file_path
        else:
            image_path = image_path_or_url

        logger.info(f"Extracting face embedding from: {image_path}")

        # Use InsightFace if available (same method as database creation)
        if face_app is not None:
            try:
                # Load image using OpenCV (same as original method)
                image = cv2.imread(image_path)
                if image is None:
                    logger.warning(f"Could not load image with OpenCV: {image_path}")
                    return generate_consistent_embedding(image_path_or_url)

                # Convert BGR to RGB (same as original method)
                rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                img = np.array(rgb)[:,:,::-1]  # Same transformation as original

                # Get faces using InsightFace
                faces = face_app.get(img)

                if not faces:
                    logger.warning(f"No faces found in image: {image_path}")
                    return generate_consistent_embedding(image_path_or_url)

                # Select largest face (same logic as original)
                faces = sorted(faces, key=lambda x:(x['bbox'][2]-x['bbox'][0])*(x['bbox'][3]-x['bbox'][1]))
                largest_face = faces[-1]  # Select largest face

                # Extract embedding
                face_embedding = largest_face['embedding'].tolist()

                logger.info(f"Successfully extracted InsightFace embedding with {len(face_embedding)} dimensions")
                return face_embedding

            except Exception as e:
                logger.warning(f"InsightFace processing failed: {str(e)}, falling back to consistent embedding")
                return generate_consistent_embedding(image_path_or_url)

        # Fallback to consistent embedding if InsightFace not available
        else:
            logger.info("InsightFace not available, using consistent embedding fallback")
            return generate_consistent_embedding(image_path_or_url)

    except Exception as e:
        logger.error(f"Error extracting face embedding from {image_path_or_url}: {str(e)}")
        # Return a consistent embedding based on URL/path
        return generate_consistent_embedding(image_path_or_url)

    finally:
        # Clean up temporary file if it was created
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
                logger.info(f"Cleaned up temporary file: {temp_file_path}")
            except Exception as e:
                logger.warning(f"Failed to clean up temporary file {temp_file_path}: {str(e)}")


def generate_consistent_embedding(identifier: str) -> list:
    """Generate a consistent embedding based on an identifier (path/URL)"""
    # Use hash of identifier to generate consistent random seed
    import hashlib
    hash_object = hashlib.md5(identifier.encode())
    seed = int(hash_object.hexdigest(), 16) % (2**32)

    # Generate consistent random embedding (512-dimensional to match InsightFace)
    random.seed(seed)
    embedding = [random.random() for _ in range(512)]
    random.seed()  # Reset random seed

    return embedding


def generate_face_embedding_from_image_props(identifier: str, width: int, height: int,
                                           mean_intensity: float, variance: float) -> list:
    """Generate embedding based on image properties"""
    try:
        # Use image properties plus identifier to create consistent embedding
        import hashlib
        feature_string = f"{identifier}_{width}_{height}_{mean_intensity:.2f}_{variance:.2f}"
        hash_object = hashlib.md5(feature_string.encode())
        seed = int(hash_object.hexdigest(), 16) % (2**32)

        # Generate consistent embedding (512-dimensional to match InsightFace)
        random.seed(seed)
        embedding = [random.random() for _ in range(512)]
        random.seed()  # Reset random seed

        return embedding

    except Exception as e:
        logger.warning(f"Error generating face embedding from image properties: {str(e)}")
        return generate_consistent_embedding(identifier)


def generate_face_embedding_from_image_props(identifier: str, width: int, height: int,
                                           mean_intensity: float, variance: float) -> list:
    """Generate embedding based on image properties"""
    try:
        # Use image properties plus identifier to create consistent embedding
        import hashlib
        feature_string = f"{identifier}_{width}_{height}_{mean_intensity:.2f}_{variance:.2f}"
        hash_object = hashlib.md5(feature_string.encode())
        seed = int(hash_object.hexdigest(), 16) % (2**32)

        # Generate consistent embedding
        random.seed(seed)
        embedding = [random.random() for _ in range(128)]
        random.seed()  # Reset random seed

        return embedding

    except Exception as e:
        logger.warning(f"Error generating face embedding from image properties: {str(e)}")
        return generate_consistent_embedding(identifier)


def generate_face_embedding_from_roi(face_roi, identifier: str) -> list:
    """Generate embedding based on face region properties (legacy function)"""
    try:
        # This function is kept for compatibility but not used with current implementation
        return generate_consistent_embedding(identifier)

    except Exception as e:
        logger.warning(f"Error generating face embedding from ROI: {str(e)}")
        return generate_consistent_embedding(identifier)


def compare_faces(emb1: list, emb2: list) -> float:
    """Compare two embeddings using cosine similarity"""
    try:
        # Handle different embedding sizes by padding or truncating
        if len(emb1) != len(emb2):
            min_len = min(len(emb1), len(emb2))
            emb1 = emb1[:min_len]
            emb2 = emb2[:min_len]
            logger.warning(f"Embedding size mismatch, using first {min_len} dimensions")

        # Simple dot product and magnitude calculation without numpy
        dot_product = sum(a * b for a, b in zip(emb1, emb2))
        magnitude1 = sum(a * a for a in emb1) ** 0.5
        magnitude2 = sum(b * b for b in emb2) ** 0.5

        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0

        cosine_score = dot_product / (magnitude1 * magnitude2)
        # # Normalize to 0-1 range (cosine similarity can be -1 to 1)
        # normalized_score = (cosine_score + 1) / 2
        # return normalized_score
        return cosine_score
    except Exception as e:
        logger.error(f"Error comparing faces: {str(e)}")
        return 0.0


import requests
from datetime import datetime, timedelta
import threading

# Global cache for dating profiles
_dating_profiles_cache = {
    'data': None,
    'last_updated': None,
    'cache_duration': timedelta(hours=1),  # Cache for 1 hour
    'lock': threading.Lock()
}

def load_dating_profiles():
    """
    Load dating profiles from Directus API with caching

    Returns:
        List of dating profile dictionaries
    """
    with _dating_profiles_cache['lock']:
        # Check if cache is valid
        if (_dating_profiles_cache['data'] is not None and
            _dating_profiles_cache['last_updated'] is not None and
            datetime.now() - _dating_profiles_cache['last_updated'] < _dating_profiles_cache['cache_duration']):
            logger.info("Using cached dating profiles")
            return _dating_profiles_cache['data']

        # Cache is invalid or empty, fetch from API
        logger.info("Fetching dating profiles from Directus API")

        try:
            profiles = _fetch_all_profiles_from_directus()

            # Update cache
            _dating_profiles_cache['data'] = profiles
            _dating_profiles_cache['last_updated'] = datetime.now()

            logger.info(f"Successfully loaded {len(profiles)} profiles from Directus API")
            return profiles

        except Exception as e:
            logger.error(f"Failed to load profiles from Directus API: {str(e)}")

            # Return cached data if available, even if expired
            if _dating_profiles_cache['data'] is not None:
                logger.warning("Using expired cached data due to API failure")
                return _dating_profiles_cache['data']

            # Fallback to minimal dataset
            logger.warning("Using minimal fallback dataset")
            return _get_fallback_profiles()


def _fetch_all_profiles_from_directus():
    """
    Fetch all user profiles from Directus API with pagination

    Returns:
        List of processed profile dictionaries
    """
    # Get configuration from environment
    directus_base_url = os.getenv('DIRECTUS_BASE_URL', 'https://cms.copula.site')
    directus_admin_token = os.getenv('DIRECTUS_ADMIN_TOKEN')

    if not directus_admin_token:
        raise ValueError("DIRECTUS_ADMIN_TOKEN environment variable is required")

    headers = {
        'accept': 'application/json, text/plain, */*',
        'Cookie': f'directus_session_token={directus_admin_token}'
    }

    all_profiles = []
    page = 1
    limit = 100

    while True:
        url = f"{directus_base_url}/items/UserProfile"
        params = {
            'limit': limit,
            'page': page,
            'filter[status][_eq]': 'published'  # Only get published profiles
        }

        try:
            logger.info(f"Fetching page {page} from Directus API")
            response = requests.get(url, headers=headers, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()
            profiles = data.get('data', [])

            if not profiles:
                # No more profiles to fetch
                break

            # Process and add profiles
            for profile in profiles:
                processed_profile = _process_directus_profile(profile)
                if processed_profile:  # Only add valid profiles
                    all_profiles.append(processed_profile)

            # Check if we got fewer profiles than the limit (last page)
            if len(profiles) < limit:
                break

            page += 1

        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP error fetching profiles from page {page}: {str(e)}")
            break
        except Exception as e:
            logger.error(f"Error processing profiles from page {page}: {str(e)}")
            break

    return all_profiles


def _process_directus_profile(profile):
    """
    Process a raw Directus profile into the format expected by the matching system

    Args:
        profile: Raw profile dictionary from Directus API

    Returns:
        Processed profile dictionary or None if invalid
    """
    try:
        # Skip profiles without essential data
        if not profile.get('id') or not profile.get('first_name'):
            return None

        processed = {
            # Core identification fields
            'id': profile['id'],
            'status': profile.get('status', ''),
            'user': profile.get('user', ''),
            'sample_id': profile.get('sample_id', ''),

            # Personal information
            'first_name': profile.get('first_name', ''),
            'last_name': profile.get('last_name', ''),
            'gender': profile.get('gender', ''),
            'age': profile.get('age', 0),
            'height': profile.get('height', 0),  # Note: Directus uses 'height', we map to 'height_cm'
            'race': profile.get('race', ''),
            'body_type': profile.get('body_type', ''),

            # Location and background
            'city': profile.get('city', ''),
            'location': profile.get('city', ''),  # Map city to location for backward compatibility
            'language': profile.get('language', ''),
            'religion': profile.get('religion', ''),

            # Relationship and lifestyle
            'marriage_status': profile.get('marriage_status', ''),
            'desired_relationship': profile.get('desired_relationship', ''),
            'family_plan': profile.get('family_plan', ''),
            'smoke': profile.get('smoke', ''),
            'drink': profile.get('drink', ''),
            'pets': profile.get('pets', ''),

            # Professional and education
            'work': profile.get('work', ''),
            'job': profile.get('work', ''),  # Map work to job for backward compatibility
            'education': profile.get('education', ''),
            'income': profile.get('income', ''),

            # Profile content
            'essay': profile.get('essay', ''),
            'interests': profile.get('interests', ''),

            # Media
            'avatar': profile.get('avatar', ''),
            'images': profile.get('images', [])
        }

        # Convert numeric fields safely
        try:
            processed['age'] = int(profile.get('age', 0)) if profile.get('age') else 0
        except (ValueError, TypeError):
            processed['age'] = 0

        try:
            processed['height_cm'] = float(profile.get('height', 0)) if profile.get('height') else 0
        except (ValueError, TypeError):
            processed['height_cm'] = 0

        # Handle personality traits - Big 5 personality model
        # These come directly from Directus and should be text values like "Conventional", "Organised", etc.
        processed['openness'] = profile.get('openness', 'Unknown')
        processed['conscientiousness'] = profile.get('conscientiousness', 'Unknown')
        processed['extraversion'] = profile.get('extraversion', 'Unknown')
        processed['agreeableness'] = profile.get('agreeableness', 'Unknown')
        processed['neuroticism'] = profile.get('neuroticism', 'Unknown')

        # Handle values fields - Schwartz values model
        processed['traditionalism'] = profile.get('traditionalism', 'Unknown')
        processed['conformity'] = profile.get('conformity', 'Unknown')

        # Handle interests - should be a comma-separated string
        interests = profile.get('interests', '')
        if isinstance(interests, list):
            processed['interests'] = ', '.join(str(item) for item in interests if item)
        else:
            processed['interests'] = str(interests) if interests else ''

        # Handle income - convert to string if it's numeric
        income = profile.get('income', '')
        if isinstance(income, (int, float)):
            processed['income'] = str(income)
        else:
            processed['income'] = str(income) if income else ''

        return processed

    except Exception as e:
        logger.warning(f"Error processing profile {profile.get('id', 'unknown')}: {str(e)}")
        return None


def _get_fallback_profiles():
    """
    Get minimal fallback profiles when API is unavailable

    Returns:
        List of minimal profile dictionaries
    """
    return [
        {
            # Core identification
            'id': 1, 'status': 'published', 'user': 'sample-user-1', 'sample_id': 'fallback-1',

            # Personal information
            'first_name': 'Sample1', 'last_name': 'User', 'gender': 'm', 'age': 25,
            'height_cm': 175, 'race': 'asian', 'body_type': 'fit',

            # Location and background
            'city': 'Sample City', 'location': 'Sample City', 'language': 'English',
            'religion': 'Buddhism',

            # Relationship and lifestyle
            'marriage_status': 'single', 'desired_relationship': 'Long-term relationship',
            'family_plan': 'wants kids', 'smoke': 'no', 'drink': 'socially', 'pets': 'likes dogs',

            # Professional and education
            'work': 'Software Engineer', 'job': 'Software Engineer',
            'education': 'Undergraduate Degree (BA / BSc / BSSc / Other)', 'income': '3',

            # Personality traits
            'openness': 'Imaginative', 'conscientiousness': 'Organised',
            'extraversion': 'Outgoing', 'agreeableness': 'Warm', 'neuroticism': 'Calm',
            'traditionalism': 'Traditional', 'conformity': 'Compliant',

            # Profile content
            'interests': 'Reading, Music, Travel',
            'essay': 'Sample profile for testing the fallback system',

            # Media
            'avatar': '', 'images': []
        },
        {
            # Core identification
            'id': 2, 'status': 'published', 'user': 'sample-user-2', 'sample_id': 'fallback-2',

            # Personal information
            'first_name': 'Sample2', 'last_name': 'User', 'gender': 'f', 'age': 28,
            'height_cm': 165, 'race': 'white', 'body_type': 'average',

            # Location and background
            'city': 'Another City', 'location': 'Another City', 'language': 'English, Spanish',
            'religion': 'Christianity',

            # Relationship and lifestyle
            'marriage_status': 'single', 'desired_relationship': 'Short-term relationship',
            'family_plan': 'unsure about kids', 'smoke': 'no', 'drink': 'rarely', 'pets': 'has cats',

            # Professional and education
            'work': 'Marketing Manager', 'job': 'Marketing Manager',
            'education': 'Graduate Degree (MA / MS / MEng / Other)', 'income': '4',

            # Personality traits
            'openness': 'Conventional', 'conscientiousness': 'Spontaneous',
            'extraversion': 'Reserved', 'agreeableness': 'Independent', 'neuroticism': 'Sensitive',
            'traditionalism': 'Progressive', 'conformity': 'Autonomous',

            # Profile content
            'interests': 'Sports, Cooking, Art',
            'essay': 'Another sample profile for testing the fallback system',

            # Media
            'avatar': '', 'images': []
        }
    ]


def clear_dating_profiles_cache():
    """
    Clear the dating profiles cache to force a fresh fetch from API
    """
    with _dating_profiles_cache['lock']:
        _dating_profiles_cache['data'] = None
        _dating_profiles_cache['last_updated'] = None
        logger.info("Dating profiles cache cleared")


@lru_cache(maxsize=1)
def load_face_embeddings():
    """
    Load face embeddings from pickle file with caching

    Returns:
        Dictionary of face embeddings
    """
    embeddings_path = pathlib.Path(__file__).parent / "files" / "dating_app_2k_embedding_s512.pickle"
    if not embeddings_path.exists():
        raise FileNotFoundError(f"Embeddings file not found: {embeddings_path}")

    try:
        with open(embeddings_path, 'rb') as f:
            embedding_data = pickle.load(f)
        return embedding_data
    except Exception as e:
        logger.warning(f"Could not load embeddings pickle file: {str(e)}")
        # Use empty embeddings data as fallback
        return {}


def perform_profile_matching_v2(synthetic_profile: dict, input_image_path: str, include_explanations: bool = True) -> dict:
    """
    Perform complete profile matching using NEW matching logic v2 with JSON objects

    New weights:
    - Face embedding: 50% (w1)
    - Personality: 20% (w2)
    - Interests: 10% (w3)
    - Values (traditionalism + conformity): 10% (w4)
    - Race: 10% (w5)

    Args:
        synthetic_profile: Synthetic profile dictionary
        input_image_path: Path to input image for face embedding
        include_explanations: Whether to generate AI explanations (slower)

    Returns:
        Dictionary containing matches and explanations
    """
    try:
        # Load data
        candidates = load_dating_profiles()
        face_embeddings = load_face_embeddings()

        # Filter candidates by opposite gender
        synthetic_gender = synthetic_profile['gender']
        target_gender = 'f' if synthetic_gender == 'm' else 'm'
        candidates = [c for c in candidates if c['gender'] == target_gender]

        logger.info(f"Found {len(candidates)} candidates of opposite gender ({target_gender})")

        # Generate face embedding for synthetic profile
        synthetic_face_embedding = get_face_embedding(input_image_path)

        # Extract synthetic profile attributes
        synthetic_interests = synthetic_profile.get('interests', '').split(', ') if isinstance(synthetic_profile.get('interests'), str) else synthetic_profile.get('interests', [])

        # Handle personality - in new_data.csv, personality traits are stored as individual text values
        # Convert them to a list for comparison
        personality_fields = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism']
        synthetic_personality_traits = []
        for field in personality_fields:
            if field in synthetic_profile and synthetic_profile[field]:
                synthetic_personality_traits.append(synthetic_profile[field])

        # Also check for personality_tags if available (for backward compatibility)
        if 'personality_tags' in synthetic_profile and synthetic_profile['personality_tags']:
            synthetic_personality_tags = synthetic_profile['personality_tags'].split(', ') if isinstance(synthetic_profile['personality_tags'], str) else synthetic_profile['personality_tags']
            synthetic_personality_traits.extend(synthetic_personality_tags)

        # Extract values for new matching logic
        synthetic_traditionalism = synthetic_profile.get('traditionalism', '')
        synthetic_conformity = synthetic_profile.get('conformity', '')

        # Calculate matching scores for each candidate
        matches = []
        for candidate in candidates:
            match_data = candidate.copy()

            # 1. Face similarity using real embeddings (50% weight)
            candidate_file_name = candidate['file_name']
            if candidate_file_name in face_embeddings:
                candidate_embedding = face_embeddings[candidate_file_name]
                face_score = compare_faces(synthetic_face_embedding, candidate_embedding)
            else:
                face_score = random.uniform(-0.1, 0.1)
                logger.warning(f"No face embedding found for {candidate_file_name}, using random score")

            # 2. Personality matching (20% weight)
            candidate_personality_traits = []

            # Get personality traits from individual fields (new_data.csv format)
            for field in personality_fields:
                if field in candidate and candidate[field]:
                    candidate_personality_traits.append(candidate[field])

            # Also check for personality_tags if available (backward compatibility)
            if 'personality_tags' in candidate and candidate['personality_tags']:
                candidate_personality_tags = candidate['personality_tags'].split(', ') if isinstance(candidate['personality_tags'], str) else candidate['personality_tags']
                candidate_personality_traits.extend(candidate_personality_tags)

            personality_overlap = len(set(synthetic_personality_traits) & set(candidate_personality_traits))

            # 3. Interest matching (10% weight) - NEW: Use text embeddings
            candidate_interests = candidate['interests'].split(', ') if 'interests' in candidate else []

            interest_similarity_score = calculate_interest_similarity_placeholder(synthetic_interests, candidate_interests)

            # Convert similarity score to overlap-like score for compatibility
            interest_overlap = interest_similarity_score * 5  # Scale to 0-5 range

            # 4. Values matching (10% weight) - NEW in v2
            values_score = 0
            if 'traditionalism' in candidate and 'conformity' in candidate:
                candidate_traditionalism = candidate['traditionalism']
                candidate_conformity = candidate['conformity']

                # Count matching values (0-2)
                if synthetic_traditionalism == candidate_traditionalism:
                    values_score += 1
                if synthetic_conformity == candidate_conformity:
                    values_score += 1

            # 5. Race matching (10% weight)
            synthetic_race = synthetic_profile.get('race', '')
            candidate_race = candidate.get('race', '')
            race_score = 1 if synthetic_race == candidate_race else 0

            # Normalize scores to 0-1 range
            interest_norm = interest_overlap / 5.0  # Max 5 interests
            personality_norm = personality_overlap / 5.0  # Max 5 personality traits
            face_norm = (face_score + 1) / 2  # Cosine similarity -1 to 1 -> 0 to 1
            values_norm = values_score / 2.0  # Max 2 values (traditionalism + conformity)

            # Calculate weighted average with NEW weights
            weights = [0.5, 0.2, 0.1, 0.1, 0.1]  # face, personality, interests, values, race

            weighted_average = (
                face_norm * weights[0] +
                personality_norm * weights[1] +
                interest_norm * weights[2] +
                values_norm * weights[3] +
                race_score * weights[4]
            )

            match_data.update({
                'same_interests': interest_overlap,
                'same_personality': personality_overlap,
                'cosine_score': face_score,
                'same_interests_norm': interest_norm,
                'same_personality_norm': personality_norm,
                'cosine_score_norm': face_norm,
                'same_race': race_score,
                'values_score': values_score,  # NEW
                'values_norm': values_norm,    # NEW
                'weighted_average': weighted_average
            })

            matches.append(match_data)

        # Apply the same shifting logic as original
        if matches:
            old_min = min(m['weighted_average'] for m in matches)
            old_max = max(m['weighted_average'] for m in matches)

            if old_max >= 0.8:
                offset = 0
            elif old_max >= 0.7:
                offset = 0.1
            elif old_max >= 0.6:
                offset = 0.2
            else:
                offset = 0.3

            new_min = old_min + offset
            new_max = old_max + offset
            score_range = new_max - new_min if new_max != new_min else 1.0

            for m in matches:
                if old_max != old_min:
                    shifted_value = ((m['weighted_average'] - old_min) / (old_max - old_min)) * score_range
                    m['weighted_average_shifted'] = new_min + shifted_value
                else:
                    m['weighted_average_shifted'] = new_min

        # Sort by weighted average (shifted)
        matches.sort(key=lambda x: x.get('weighted_average_shifted', x['weighted_average']), reverse=True)

        # Get top 5 matches
        top_5_matches = matches[:5]

        # Generate explanations if requested
        top_5_with_explanations = []
        if include_explanations:
            for match in top_5_matches:
                explanation = generate_match_explanation_v2(synthetic_profile, match)
                match_with_explanation = match.copy()
                match_with_explanation['explanation'] = explanation
                top_5_with_explanations.append(match_with_explanation)
        else:
            top_5_with_explanations = top_5_matches

        return {
            'matches': matches,
            'top_5_with_explanations': top_5_with_explanations,
            'total_candidates': len(candidates)
        }

    except Exception as e:
        logger.error(f"Error in profile matching v2: {str(e)}")
        raise








def calculate_interest_similarity_placeholder(synthetic_interests: list, candidate_interests: list) -> float:
    """
    Placeholder function for interest similarity calculation using text embeddings

    TODO: Replace with Gemini-based text embedding similarity

    Args:
        synthetic_interests: List of synthetic profile interests
        candidate_interests: List of candidate interests

    Returns:
        Similarity score between 0 and 1
    """
    try:
        # Placeholder logic: Simple text overlap for now
        # This will be replaced with Gemini text embeddings

        if not synthetic_interests or not candidate_interests:
            return 0.0

        # Simple word-based similarity as placeholder
        synthetic_words = set()
        for interest in synthetic_interests:
            synthetic_words.update(interest.lower().split())

        candidate_words = set()
        for interest in candidate_interests:
            candidate_words.update(interest.lower().split())

        if not synthetic_words or not candidate_words:
            return 0.0

        # Jaccard similarity as placeholder
        intersection = len(synthetic_words & candidate_words)
        union = len(synthetic_words | candidate_words)

        similarity = intersection / union if union > 0 else 0.0

        # Add some randomness to simulate embedding similarity
        similarity += random.uniform(-0.1, 0.1)
        similarity = max(0.0, min(1.0, similarity))  # Clamp to [0, 1]

        return similarity

    except Exception as e:
        logger.warning(f"Error calculating interest similarity: {str(e)}")
        return 0.0


def generate_match_explanation_v2(synthetic_profile: dict, match: dict, user_profile: dict = None) -> str:
    """
    Generate AI explanation for a match using NEW matching logic v2 with optimized engine

    Uses the optimized MatchExplanationEngine for comprehensive explanations
    including personality, values, and interests compatibility.

    Args:
        synthetic_profile: Synthetic profile dictionary
        match: Match profile dictionary
        user_profile: Optional user profile for additional context

    Returns:
        Comprehensive explanation string combining multiple explanation types
    """
    try:
        from match_explanation_engine import MatchExplanationEngine

        # Initialize the explanation engine
        engine = MatchExplanationEngine()

        # Generate comprehensive explanations
        explanations = engine.generate_comprehensive_explanation(
            match=match,
            synthetic_profile=synthetic_profile,
            user_profile=user_profile
        )

        # Combine explanations into a cohesive narrative

        # Build comprehensive explanation
        parts = []

        # Add spark explanation (main compatibility)
        if explanations.get('spark_explanation'):
            parts.append(explanations['spark_explanation'])

        # Add match vibe explanation
        if explanations.get('match_vibe'):
            parts.append(explanations['match_vibe'])

        # Add first date idea
        if explanations.get('first_date_idea'):
            parts.append(explanations['first_date_idea'])

        # Join all parts with appropriate spacing
        full_explanation = ' '.join(parts)

        # Clean up the explanation
        full_explanation = full_explanation.replace('"', '').replace('\n', ' ').strip()

        # Ensure it's not too long (limit to ~200 words)
        words = full_explanation.split()
        if len(words) > 200:
            full_explanation = ' '.join(words[:200]) + '...'

        return full_explanation

    except ImportError:
        logger.warning("MatchExplanationEngine not available, using fallback")
        return _generate_fallback_explanation_v2(synthetic_profile, match)
    except Exception as e:
        logger.error(f"Error generating match explanation v2: {str(e)}")
        return _generate_fallback_explanation_v2(synthetic_profile, match)


def _generate_fallback_explanation_v2(synthetic_profile: dict, match: dict) -> str:
    """Fallback explanation generator if the main engine fails"""
    try:
        match_name = match.get('first_name', match.get('name', 'this person'))

        # Simple compatibility check
        shared_interests = []
        synth_interests = synthetic_profile.get('interests', '').split(',') if synthetic_profile.get('interests') else []
        match_interests = match.get('interests', '').split(',') if match.get('interests') else []

        for interest in synth_interests:
            if interest.strip() in [m.strip() for m in match_interests]:
                shared_interests.append(interest.strip())

        if shared_interests:
            return f"Both you and {match_name} share interests in {', '.join(shared_interests[:2])}. This creates a strong foundation for connection and compatibility."
        else:
            return f"You and {match_name} complement each other well with your personality traits and values, creating great potential for a meaningful relationship."

    except Exception as e:
        logger.error(f"Error in fallback explanation: {str(e)}")
        return "Great match based on shared interests, personality compatibility, and values alignment!"


@app.get("/")
async def root():
    """API information endpoint."""
    return {
        "message": "Image Upload & ComfyUI Workflow API is running",
        "version": "1.0.0",
        "endpoints": {
            "upload_file": "POST /upload-image (multipart/form-data)",
            "upload_base64": "POST /upload-image-base64 (JSON)",
            "execute_workflow": "POST /execute-workflow (JSON)",
            "create_synthetic_profile": "POST /create-synthetic-profile (JSON)",
            "find_profile_matches": "POST /find-profile-matches (JSON)",
            "create_synthetic_profile_matching": "POST /create-synthetic-profile-matching (JSON) [LEGACY]",
            "delete_image": "DELETE /delete-image/{filename}",
            "health": "GET /health",
            "docs": "GET /docs"
        },
        "storage": {
            "bucket": BUCKET_NAME,
            "folder": FOLDER_PATH,
            "supported_formats": list(SUPPORTED_IMAGE_TYPES.keys()),
            "max_file_size_mb": MAX_FILE_SIZE / (1024 * 1024)
        },
        "workflow": {
            "default_cloudrun_url": "https://comfyui-cloudrun-435103809426.asia-southeast1.run.app",
            "default_output_node": "51",
            "default_weight": 0.5,
            "weight_range": "0.0 - 1.0 (controls PuLID strength and safety threshold)"
        }
    }


@app.get("/health")
async def health_check():
    """Detailed health check including GCP connectivity."""
    health_status = {
        "status": "healthy",
        "gcp_storage": "connected" if storage_client else "disconnected",
        "bucket": BUCKET_NAME,
        "folder": FOLDER_PATH
    }
    
    if not storage_client:
        health_status["status"] = "unhealthy"
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content=health_status
        )
    
    return health_status


@app.post("/refresh-profiles-cache")
async def refresh_profiles_cache():
    """
    Manually refresh the dating profiles cache from Directus API
    """
    try:
        clear_dating_profiles_cache()
        profiles = load_dating_profiles()

        return {
            "success": True,
            "message": "Profiles cache refreshed successfully",
            "profiles_count": len(profiles),
            "cache_updated": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error refreshing profiles cache: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to refresh profiles cache: {str(e)}"
        )














def upload_image_from_url_to_input_folder(image_url: str) -> str:
    """
    Download an image from a URL and upload it to the input folder in GCS.

    Args:
        image_url: URL of the image to download and upload

    Returns:
        New public URL of the uploaded image in the input folder

    Raises:
        HTTPException: If download or upload fails
    """
    try:
        import requests
        from urllib.parse import urlparse
        import os

        logger.info(f"Downloading image from URL: {image_url}")

        # Download the image
        response = requests.get(image_url, timeout=30)
        response.raise_for_status()

        # Validate content type
        content_type = response.headers.get('content-type', '')
        if not content_type.startswith('image/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"URL does not point to an image. Content-Type: {content_type}"
            )

        # Extract filename from URL
        parsed_url = urlparse(image_url)
        original_filename = os.path.basename(parsed_url.path)

        # If no filename in URL, generate one based on content type
        if not original_filename or '.' not in original_filename:
            file_extension = SUPPORTED_IMAGE_TYPES.get(content_type, ['.png'])[0]
            original_filename = f"downloaded_image{file_extension}"

        # Generate unique filename with input folder path
        unique_filename = storage_client.generate_unique_filename(
            original_filename=original_filename,
            folder_path=FOLDER_PATH
        )

        logger.info(f"Uploading downloaded image: {original_filename} -> {unique_filename}")

        # Create file-like object from downloaded bytes
        image_file = io.BytesIO(response.content)

        # Upload file to GCS input folder
        public_url = storage_client.upload_file_from_memory(
            file_obj=image_file,
            destination_blob_name=unique_filename,
            content_type=content_type,
            make_public=True
        )

        if not public_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upload downloaded image to Google Cloud Storage"
            )

        logger.info(f"Successfully uploaded image to: {public_url}")
        return public_url

    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to download image from URL {image_url}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to download image from URL: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error uploading image from URL {image_url}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal error while processing image URL: {str(e)}"
        )


@app.post("/execute-workflow")
def execute_workflow_endpoint(request: WorkflowRequest):
    """
    Execute ComfyUI workflow with given prompt and image URL.

    Args:
        request: Workflow request containing prompt, image_url, and optional parameters

    Returns:
        JSON response with workflow execution results and timing
    """
    try:
        logger.info(f"Executing workflow with prompt: {request.prompt[:50]}...")

        # Check if image URL needs to be uploaded to input folder
        expected_input_prefix = f"https://storage.googleapis.com/{BUCKET_NAME}/{FOLDER_PATH}/"
        actual_image_url = request.image_url

        if not request.image_url.startswith(expected_input_prefix):
            logger.info(f"Image URL not in input folder. Uploading to input folder: {request.image_url}")

            # Check if storage client is available for upload
            if not storage_client:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="GCP Storage service is not available for image upload"
                )

            # Upload image to input folder
            actual_image_url = upload_image_from_url_to_input_folder(request.image_url)
            logger.info(f"Image uploaded to input folder: {actual_image_url}")
        else:
            logger.info(f"Image URL already in input folder: {request.image_url}")

        # Execute workflow
        start_time = time.time()
        result = execute_workflow_api(
            image_url=actual_image_url,
            input_prompt=request.prompt,
            weight=request.weight,
            cloudrun_url=request.cloudrun_url,
            output_node=request.output_node
        )
        total_time = time.time() - start_time

        # Prepare response
        response_data = {
            "success": True,
            "message": "Workflow executed successfully",
            "data": {
                "output_url": result["output_url"],
                "execution_time_seconds": result["execution_time_seconds"],
                "total_api_time_seconds": round(total_time, 2),
                "filename": result["filename"],
                "input": {
                    "prompt": request.prompt,
                    "image_url": request.image_url,
                    "weight": request.weight,
                    "cloudrun_url": request.cloudrun_url,
                    "output_node": request.output_node
                },
                "workflow_details": {
                    "enhanced_prompt": result["prompt_used"],
                    "input_image_url": result["input_image_url"],
                    "weight_used": result["weight_used"],
                    "output_node_used": result["output_node"]
                },
                "image_processing": {
                    "original_image_url": request.image_url,
                    "processed_image_url": actual_image_url,
                    "was_uploaded_to_input_folder": actual_image_url != request.image_url,
                    "input_folder_path": f"{BUCKET_NAME}/{FOLDER_PATH}/"
                }
            }
        }

        logger.info(f"Workflow completed in {total_time:.2f} seconds. Output: {result['output_url']}")
        return response_data

    except HTTPException as he:
        # For HTTP exceptions that occur after image processing, include image processing info
        if 'actual_image_url' in locals() and actual_image_url != request.image_url:
            # Image was processed/uploaded, include this info in error response
            error_detail = {
                "error": str(he.detail),
                "image_processing": {
                    "original_image_url": request.image_url,
                    "processed_image_url": actual_image_url,
                    "was_uploaded_to_input_folder": True,
                    "input_folder_path": f"{BUCKET_NAME}/{FOLDER_PATH}/",
                    "note": "Image was successfully uploaded to input folder before workflow execution failed"
                }
            }
            raise HTTPException(
                status_code=he.status_code,
                detail=error_detail
            )
        else:
            raise
    except Exception as e:
        logger.error(f"Unexpected error in workflow endpoint: {str(e)}")
        # Include image processing info if available
        if 'actual_image_url' in locals() and actual_image_url != request.image_url:
            error_detail = {
                "error": f"Internal server error: {str(e)}",
                "image_processing": {
                    "original_image_url": request.image_url,
                    "processed_image_url": actual_image_url,
                    "was_uploaded_to_input_folder": True,
                    "input_folder_path": f"{BUCKET_NAME}/{FOLDER_PATH}/",
                    "note": "Image was successfully uploaded to input folder before workflow execution failed"
                }
            }
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=error_detail
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}"
            )











@app.post("/find-profile-matches-v2")
def find_profile_matches_v2_endpoint(request: ProfileMatchingRequest):
    """
    Find top 5 matches for a synthetic profile using NEW matching logic v2

    This endpoint implements the new matching algorithm with updated weights:
    - Face embedding: 50% (w1)
    - Personality: 20% (w2)
    - Interests: 10% (w3)
    - Values (traditionalism + conformity): 10% (w4)
    - Race: 10% (w5)

    Key differences from v1:
    - Reduced face embedding weight from 60% to 50%
    - Increased personality weight from 10% to 20%
    - Reduced interest weight from 20% to 10%
    - Added values matching (traditionalism + conformity): 10%
    - Race weight remains 10%

    Args:
        request: Synthetic profile data, image URL, and optional explanation flag

    Returns:
        JSON response with top 5 matches using new matching algorithm
    """
    try:
        logger.info(f"Finding matches for synthetic profile using NEW matching logic v2")

        # Validate synthetic profile data
        synthetic_profile = request.synthetic_profile
        if not synthetic_profile:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Synthetic profile data must be provided"
            )

        if 'gender' not in synthetic_profile:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Synthetic profile must include gender"
            )

        if not request.image_url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Image URL must be provided for face embedding"
            )

        # Validate that synthetic profile has required fields for new matching logic
        required_fields = ['traditionalism', 'conformity']
        for field in required_fields:
            if field not in synthetic_profile:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Synthetic profile must include {field} for v2 matching"
                )

        # Find matches using new logic
        start_time = time.time()
        matches_result = find_profile_matches_v2(
            synthetic_profile,
            request.image_url,
            include_explanations=request.include_explanations
        )
        matching_time = time.time() - start_time

        response_data = {
            "success": True,
            "message": "Profile matches found successfully using NEW matching logic v2",
            "data": {
                "matches": matches_result['top_5_with_explanations'],
                "total_candidates": matches_result['total_candidates'],
                "all_matches": matches_result['match_df'][:10],  # Return top 10 for reference
                "matching_algorithm": "v2",
                "weights": {
                    "face_embedding": 0.5,
                    "personality": 0.2,
                    "interests": 0.1,
                    "values": 0.1,
                    "race": 0.1
                }
            },
            "matching_time_seconds": round(matching_time, 3)
        }

        logger.info(f"Successfully found {matches_result['total_candidates']} matches using v2 logic in {matching_time:.3f} seconds")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during profile matching v2: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.post("/create-synthetic-profile-v2")
def create_synthetic_profile_v2_endpoint(user_profile: dict):
    """
    Create synthetic profile using NEW logic v2

    This endpoint creates a synthetic profile from a user profile using the new logic:
    1. Personality: Pick 3 random traits from user's 5 personality traits
    2. Interests: Generate 3 new interests from same categories as user's interests
    3. Values: Copy user's traditionalism and conformity values
    4. Face: Use provided image
    5. Race: Keep same as user

    Args:
        user_profile: Original user profile data

    Returns:
        JSON response with synthetic profile
    """
    try:
        logger.info(f"Creating synthetic profile v2 for user")

        # Validate required fields
        required_fields = ['gender', 'interests', 'traditionalism', 'conformity']
        for field in required_fields:
            if field not in user_profile:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"User profile must include {field}"
                )

        # Create synthetic profile
        start_time = time.time()
        result = create_synthetic_profile_v2(user_profile)
        creation_time = time.time() - start_time

        response_data = {
            "success": True,
            "message": "Synthetic profile v2 created successfully",
            "data": {
                "synthetic_profile": result['synthetic_profile'],
                "original_interests": result['original_interests'],
                "synthetic_interests": result['synthetic_interests'],
                "original_personality": result['original_personality'],
                "selected_personality": result['selected_personality']
            },
            "creation_time_seconds": round(creation_time, 3)
        }

        logger.info(f"Successfully created synthetic profile v2 in {creation_time:.3f} seconds")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating synthetic profile v2: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )







@app.post("/generate-personality-tags")
def generate_personality_tags(request: PersonalityTagsRequest):
    """
    Generate personality tags based on personality trait scores

    This endpoint clones the exact logic from generate_synthetic_profile
    for generating personality tags based on the Big Five personality traits.

    Args:
        request: PersonalityTagsRequest containing openness, conscientiousness,
                extraversion, and agreeableness scores

    Returns:
        Personality tags as a comma-separated string
    """
    try:
        start_time = time.time()

        # Validate input scores (should be between 0 and 10)
        for trait_name, score in [
            ('openness', request.openness),
            ('conscientiousness', request.conscientiousness),
            ('extraversion', request.extraversion),
            ('agreeableness', request.agreeableness)
        ]:
            if not (0 <= score <= 10):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid {trait_name} score: {score}. Must be between 0 and 10."
                )

        # Clone the exact personality tags generation logic from generate_synthetic_profile
        personality_tags_data = {
            'High': ['Imaginative', 'Organised', 'Outgoing', 'Warm', 'Sensitive'],
            'Medium': ['Inquisitive', 'Adaptable', 'Friendly', 'Considerate', 'Perceptive'],
            'Low': ['Conventional', 'Spontaneous', 'Reserved', 'Independent', 'Calm']
        }

        # Generate neuroticism score (same logic as in generate_synthetic_profile)
        import random as rand
        neuroticism = max(0, min(10, round(rand.gauss(4.5, 1))))

        # Create trait scores dictionary
        trait_scores = {
            'openness': request.openness,
            'conscientiousness': request.conscientiousness,
            'extraversion': request.extraversion,
            'agreeableness': request.agreeableness,
            'neuroticism': neuroticism
        }

        # Generate personality tags based on scores (exact same logic)
        personality_tags = []
        trait_names = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism']

        for i, trait in enumerate(trait_names):
            score = trait_scores[trait]
            if score <= 3:
                tag_category = 'Low'
            elif score <= 7:
                tag_category = 'Medium'
            else:
                tag_category = 'High'
            personality_tags.append(personality_tags_data[tag_category][i])

        # Join tags as comma-separated string
        personality_tags_string = ', '.join(personality_tags)

        end_time = time.time()
        generation_time = end_time - start_time

        logger.info(f"Successfully generated personality tags: {personality_tags_string}")

        return {
            "status": "success",
            "data": {
                "personality_tags": personality_tags_string,
                "personality_tags_list": personality_tags,
                "trait_scores_used": trait_scores,
                "score_categories": {
                    trait_names[i]: ('High' if trait_scores[trait_names[i]] > 7
                                   else 'Medium' if trait_scores[trait_names[i]] > 3
                                   else 'Low')
                    for i in range(len(trait_names))
                }
            },
            "generation_time_seconds": round(generation_time, 6)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating personality tags: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.post("/generate-image-gemini")
def generate_image_gemini_endpoint(request: GeminiImageGenerationRequest):
    """
    Generate image using Google Gemini with source image and prompt

    This endpoint uses Google Gemini's image generation capabilities to create
    new images based on a source image and text prompt.

    Args:
        request: Contains image_url and optional prompt

    Returns:
        JSON response with generated image data
    """
    try:
        logger.info(f"Generating image with Gemini for URL: {request.image_url}")

        # Validate image URL
        if not request.image_url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="image_url is required"
            )

        # Generate image
        start_time = time.time()
        result = generate_image_with_gemini(request.image_url, request.prompt)
        generation_time = time.time() - start_time

        # Extract data from the nested result structure
        result_data = result.get('data', {})

        response_data = {
            "success": True,
            "message": "Image generated successfully with Gemini",
            "data": {
                "generated_image_url": result_data.get('generated_image_url'),
                "generated_image_base64": result_data.get('generated_image_base64'),  # Fallback if upload fails
                "filename": result_data.get('filename'),
                "bucket_path": result_data.get('bucket_path'),
                "upload_error": result_data.get('upload_error'),
                "prompt_used": result_data.get('prompt_used'),
                "source_image_url": result_data.get('source_image_url')
            },
            "generation_time_seconds": round(generation_time, 3)
        }

        logger.info(f"Successfully generated image with Gemini in {generation_time:.3f} seconds")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating image with Gemini: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.get("/profile/{profile_id}")
def get_profile_by_id(profile_id: int):
    """
    Get profile data from dating_app_sample_100_essay_tag.csv by ID

    Args:
        profile_id: The ID of the profile to retrieve

    Returns:
        Profile data as JSON
    """
    try:
        # Path to the CSV file
        csv_file_path = pathlib.Path(__file__).parent / "files" / "dating_app_sample_100_essay_tag.csv"

        if not csv_file_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"CSV file not found: {csv_file_path}"
            )

        # Read the CSV file and find the profile by ID
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            csv_reader = csv.DictReader(file)

            for row in csv_reader:
                if int(row['id']) == profile_id:
                    # Convert numeric fields to appropriate types
                    profile_data = {
                        'id': int(row['id']),
                        'file_name': row['file_name'],
                        'first_name': row['first_name'],
                        'last_name': row['last_name'],
                        'height_cm': float(row['height_cm']),
                        'race': row['race'],
                        'age': int(row['age']),
                        'status': row['status'],
                        'gender': row['gender'],
                        'orientation': row['orientation'],
                        'body_type': row['body_type'],
                        'diet': row['diet'],
                        'drinks': row['drinks'],
                        'smokes': row['smokes'],
                        'education': row['education'],
                        'job': row['job'],
                        'location': row['location'],
                        'family_plan': row['family_plan'],
                        'pets': row['pets'],
                        'desired_relationship': row['desired_relationship'],
                        'openness': float(row['openness']),
                        'conscientiousness': float(row['conscientiousness']),
                        'extraversion': float(row['extraversion']),
                        'agreeableness': float(row['agreeableness']),
                        'neuroticism': float(row['neuroticism']),
                        'interests': row['interests'],
                        'essay0': row['essay0'],
                        'personality_tags': row['personality_tags']
                    }

                    logger.info(f"Successfully retrieved profile for ID: {profile_id}")
                    return {
                        "status": "success",
                        "data": profile_data
                    }

        # If we get here, the profile ID was not found
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Profile with ID {profile_id} not found"
        )

    except HTTPException:
        raise
    except ValueError as e:
        logger.error(f"Invalid profile ID format: {profile_id}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid profile ID format: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error retrieving profile {profile_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.get("/profiles")
def get_all_profiles(skip: int = 0, limit: int = 100):
    """
    Get all profiles from dating_app_sample_100_essay_tag.csv with optional pagination

    Args:
        skip: Number of profiles to skip (default: 0)
        limit: Maximum number of profiles to return (default: 100)

    Returns:
        List of profile data as JSON
    """
    try:
        # Path to the CSV file
        csv_file_path = pathlib.Path(__file__).parent / "files" / "dating_app_sample_100_essay_tag.csv"

        if not csv_file_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"CSV file not found: {csv_file_path}"
            )

        profiles = []

        # Read the CSV file
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            csv_reader = csv.DictReader(file)

            for i, row in enumerate(csv_reader):
                # Skip profiles based on pagination
                if i < skip:
                    continue

                # Stop if we've reached the limit
                if len(profiles) >= limit:
                    break

                # Convert numeric fields to appropriate types
                profile_data = {
                    'id': int(row['id']),
                    'file_name': row['file_name'],
                    'first_name': row['first_name'],
                    'last_name': row['last_name'],
                    'height_cm': float(row['height_cm']),
                    'age': int(row['age']),
                    'status': row['status'],
                    'gender': row['gender'],
                    'orientation': row['orientation'],
                    'body_type': row['body_type'],
                    'diet': row['diet'],
                    'drinks': row['drinks'],
                    'smokes': row['smokes'],
                    'education': row['education'],
                    'job': row['job'],
                    'location': row['location'],
                    'family_plan': row['family_plan'],
                    'pets': row['pets'],
                    'desired_relationship': row['desired_relationship'],
                    'openness': float(row['openness']),
                    'conscientiousness': float(row['conscientiousness']),
                    'extraversion': float(row['extraversion']),
                    'agreeableness': float(row['agreeableness']),
                    'neuroticism': float(row['neuroticism']),
                    'interests': row['interests'],
                    'essay0': row['essay0'],
                    'personality_tags': row['personality_tags']
                }

                profiles.append(profile_data)

        logger.info(f"Successfully retrieved {len(profiles)} profiles (skip: {skip}, limit: {limit})")
        return {
            "status": "success",
            "data": {
                "profiles": profiles,
                "count": len(profiles),
                "skip": skip,
                "limit": limit
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving profiles: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
