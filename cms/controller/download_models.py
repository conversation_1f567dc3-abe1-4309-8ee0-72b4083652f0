#!/usr/bin/env python3
"""
Script to predownload InsightFace models during Docker build
"""

import os
import sys

def download_insightface_model():
    """Download InsightFace buffalo_l model"""
    try:
        print("Predownloading InsightFace buffalo_l model...")
        
        # Create the models directory
        os.makedirs('/root/.insightface/models', exist_ok=True)
        
        # Import and initialize FaceAnalysis to trigger model download
        from insightface.app import FaceAnalysis
        
        # Initialize FaceAnalysis with buffalo_l model
        # This will automatically download the model if not present
        app = FaceAnalysis(name='buffalo_l', providers=['CPUExecutionProvider'])
        
        print("InsightFace model downloaded successfully!")
        return True
        
    except Exception as e:
        print(f"Warning: Failed to predownload InsightFace model: {e}")
        print("Model will be downloaded on first run instead.")
        return False

if __name__ == "__main__":
    success = download_insightface_model()
    if success:
        print("Model download completed successfully!")
    else:
        print("Model download failed, but container build will continue.")
    
    # Don't fail the build if model download fails
    sys.exit(0)
