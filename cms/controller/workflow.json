{"3": {"inputs": {"seed": ["52", 0], "steps": 30, "cfg": 5, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 1, "model": ["33", 0], "positive": ["22", 0], "negative": ["23", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "SDXL/cyberrealisticXL_v56.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "5": {"inputs": {"width": 512, "height": 512, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "12": {"inputs": {"image": "HK_female_above_average.PNG"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "16": {"inputs": {"pulid_file": "ip-adapter_pulid_sdxl_fp16.safetensors"}, "class_type": "PulidMode<PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load PuLID Model"}}, "17": {"inputs": {"provider": "CPU"}, "class_type": "PulidInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID)"}}, "19": {"inputs": {}, "class_type": "PulidEvaClipLoader", "_meta": {"title": "<PERSON><PERSON> (PuLID)"}}, "22": {"inputs": {"text": "portrait photo of young man, big smiling,casual style, white shirt, denim jacket, blur street background, detailed skin, realistic skin texture, dramatic, cinematic, dof, 8k uhd, dslr, high quality", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encoder (Positive Prompt)"}}, "23": {"inputs": {"text": "blurry, malformed, low quality, worst quality, artifacts, noise, text, watermark, glitch, deformed, ugly, horror, ill, big breast, long neck, sexy clothes, revealing clothes, beard", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "33": {"inputs": {"method": "fidelity", "weight": 0.30000000000000004, "start_at": 0.20000000000000004, "end_at": 0.7000000000000002, "model": ["4", 0], "pulid": ["16", 0], "eva_clip": ["19", 0], "face_analysis": ["17", 0], "image": ["12", 0]}, "class_type": "A<PERSON>ly<PERSON><PERSON><PERSON>", "_meta": {"title": "Apply PuLID"}}, "51": {"inputs": {"filename_prefix": "ComfyUI_", "images": ["57", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "52": {"inputs": {"seed": -1}, "class_type": "Seed (rgthree)", "_meta": {"title": "Seed for <PERSON><PERSON><PERSON><PERSON>"}}, "57": {"inputs": {"threshold": 0.7000000000000002, "cuda": false, "image": ["8", 0]}, "class_type": "YetAnotherSafetyChecker", "_meta": {"title": "Intercept NSFW Outputs"}}, "61": {"inputs": {"text": "0.008152971044182777", "anything": ["57", 1]}, "class_type": "easy showAnything", "_meta": {"title": "Show Any"}}}