# CMS Setup with Directus and PostgreSQL

This Docker Compose setup provides a complete Content Management System using Directus with PostgreSQL database and Google Cloud Storage integration.

## Features

- **PostgreSQL 15**: Persistent database storage
- **Directus**: Modern headless CMS with admin interface
- **Google Cloud Storage**: File storage integration with your GCS bucket
- **Persistent Data**: Database data survives container restarts and `docker-compose down`

## Prerequisites

1. Ensure `gcp-credentials.json` exists in the parent directory (already verified)
2. Docker and Docker Compose installed
3. Access to Google Cloud Storage bucket: `comfyui-data-analytic-project-424703`

## Quick Start

1. Navigate to the cms directory:
   ```bash
   cd cms
   ```

2. Start the services:
   ```bash
   docker-compose up -d
   ```

3. Wait for services to be healthy (about 30-60 seconds):
   ```bash
   docker-compose logs -f
   ```

4. Access Directus admin interface:
   - URL: http://localhost:8055
   - Email: <EMAIL>
   - Password: admin_password

## Configuration Details

### Database
- **Host**: localhost:5432
- **Database**: directus
- **User**: directus
- **Password**: directus_password
- **Data**: Stored in Docker volume `postgres_data` (persistent)

### Google Cloud Storage
- **Bucket**: comfyui-data-analytic-project-424703
- **Root Folder**: comfyui/common
- **Authentication**: Service account via gcp-credentials.json
- **Project ID**: data-analytic-project-424703

### File Uploads
- **Max Upload Size**: 100MB
- **Storage**: Files are stored in GCS bucket under `comfyui/common/`
- **Local Cache**: Temporary uploads stored in `directus_uploads` volume

## Management Commands

### Start services
```bash
docker-compose up -d
```

### Stop services (keeps data)
```bash
docker-compose down
```

### View logs
```bash
docker-compose logs -f
```

### Restart services
```bash
docker-compose restart
```

### Remove everything (including data)
```bash
docker-compose down -v
```

## Customization

### Change Admin Credentials
Edit the environment variables in `docker-compose.yml`:
```yaml
ADMIN_EMAIL: <EMAIL>
ADMIN_PASSWORD: your-secure-password
```

### Change Database Password
Update both services in `docker-compose.yml`:
```yaml
# In postgres service
POSTGRES_PASSWORD: your-new-password

# In directus service  
DB_PASSWORD: your-new-password
```

### Change GCS Folder
Modify the `STORAGE_GCS_ROOT` environment variable:
```yaml
STORAGE_GCS_ROOT: your/custom/folder
```

## Troubleshooting

### Check service health
```bash
docker-compose ps
```

### View specific service logs
```bash
docker-compose logs postgres
docker-compose logs directus
```

### Reset database
```bash
docker-compose down
docker volume rm cms_postgres_data
docker-compose up -d
```

### Verify GCS connection
Check Directus logs for GCS authentication:
```bash
docker-compose logs directus | grep -i gcs
```

## Security Notes

- Change default admin credentials before production use
- The GCS credentials file is mounted read-only
- Database password should be changed for production
- Consider using environment files for sensitive data

## Ports

- **Directus**: 8055 (http://localhost:8055)
- **PostgreSQL**: 5432 (localhost:5432)
