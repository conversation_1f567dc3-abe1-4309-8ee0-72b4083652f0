#!/bin/sh

# PostgreSQL Backup Script for Directus CMS
# Runs daily at 2 AM via cron

# Configuration
BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="directus_backup_${DATE}.sql"
BACKUP_PATH="${BACKUP_DIR}/${BACKUP_FILE}"

# Database configuration
DB_HOST="postgres"
DB_PORT="5432"
DB_USER="directus"
DB_NAME="directus"
DB_PASSWORD="directus_password"

# Create backup directory if it doesn't exist
mkdir -p ${BACKUP_DIR}

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "Error: Docker daemon is not running"
    echo "Please start Docker and try again"
    exit 1
fi

# Determine docker-compose command
if command -v docker-compose >/dev/null 2>&1; then
    DOCKER_COMPOSE="docker-compose"
elif command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
    DOCKER_COMPOSE="docker compose"
else
    echo "Error: Neither 'docker-compose' nor 'docker compose' found"
    echo "Please install Docker Compose and try again"
    exit 1
fi

# Check if PostgreSQL container is running
if ! $DOCKER_COMPOSE ps postgres | grep -q "Up"; then
    echo "Error: PostgreSQL container is not running"
    echo "Please start the services with: $DOCKER_COMPOSE up -d"
    exit 1
fi

# Create database backup using docker-compose exec
echo "Starting backup at $(date)"
echo "Creating backup: ${BACKUP_FILE}"

# Set PGPASSWORD to avoid password prompt
export PGPASSWORD="${DB_PASSWORD}"

# Create the backup
$DOCKER_COMPOSE exec -T postgres pg_dump -h localhost -p 5432 -U directus -d directus > ${BACKUP_PATH}

# Check if backup was successful
if [ $? -eq 0 ] && [ -s "${BACKUP_PATH}" ]; then
    echo "Backup completed successfully: ${BACKUP_FILE}"
    echo "Backup size: $(du -h ${BACKUP_PATH} | cut -f1)"

    # Compress the backup
    gzip ${BACKUP_PATH}
    if [ $? -eq 0 ]; then
        echo "Backup compressed: ${BACKUP_FILE}.gz"
        echo "Compressed size: $(du -h ${BACKUP_PATH}.gz | cut -f1)"
    else
        echo "Warning: Failed to compress backup, but backup file exists"
    fi

    # Keep only last 30 days of backups
    find ${BACKUP_DIR} -name "directus_backup_*.sql.gz" -mtime +30 -delete
    echo "Old backups cleaned up (kept last 30 days)"

    # List current backups
    echo "Current backups:"
    ls -lh ${BACKUP_DIR}/directus_backup_*.sql.gz 2>/dev/null | tail -5

    echo "Backup process completed at $(date)"
else
    echo "Backup failed at $(date)"
    if [ -f "${BACKUP_PATH}" ]; then
        echo "Backup file exists but may be empty or corrupted"
        echo "File size: $(du -h ${BACKUP_PATH} | cut -f1)"
        rm -f "${BACKUP_PATH}"
    fi
    exit 1
fi
