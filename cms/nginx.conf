events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Upstream for Directus
    upstream directus {
        server directus:8055;
    }

    # Upstream for Controller API
    upstream controller {
        server controller:8000;
    }

    # Upstream for Appsmith
    upstream appsmith {
        server appsmith:80;
    }

    # Server block for cms.copula.site
    server {
        listen 80;
        server_name cms.copula.site;

        # Client max body size for file uploads
        client_max_body_size 100M;

        # Proxy settings
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Proxy timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Main location block - proxy to Directus
        location / {
            proxy_pass http://directus;
            proxy_buffering off;
        }

        # WebSocket support for real-time features
        location /websocket {
            proxy_pass http://directus;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }

    # Server block for controller.copula.site
    server {
        listen 80;
        server_name controller.copula.site;

        # Client max body size for file uploads
        client_max_body_size 100M;

        # Proxy settings
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Proxy timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Main location block - proxy to Controller API
        location / {
            proxy_pass http://controller;
            proxy_buffering off;
        }

        # Health check endpoint
        location /health {
            proxy_pass http://controller/health;
        }
    }

    # Server block for appsmith.copula.site
    server {
        listen 80;
        server_name appsmith.copula.site;

        # Client max body size for file uploads
        client_max_body_size 100M;

        # Proxy settings
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Proxy timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Main location block - proxy to Appsmith
        location / {
            proxy_pass http://appsmith;
            proxy_buffering off;
        }

        # WebSocket support for Appsmith real-time features
        location /websocket {
            proxy_pass http://appsmith;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "appsmith healthy\n";
            add_header Content-Type text/plain;
        }
    }

    # Default server block for other domains
    server {
        listen 80 default_server;
        server_name _;

        return 444; # Close connection without response
    }
}
