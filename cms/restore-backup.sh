#!/bin/bash

# PostgreSQL Restore Script for Directus CMS
# Usage: ./restore-backup.sh <backup_file.sql.gz>

if [ $# -eq 0 ]; then
    echo "Usage: $0 <backup_file.sql.gz>"
    echo "Available backups:"
    ls -la backups/directus_backup_*.sql.gz 2>/dev/null || echo "No backups found"
    exit 1
fi

BACKUP_FILE=$1
BACKUP_PATH="backups/${BACKUP_FILE}"

# Check if backup file exists
if [ ! -f "${BACKUP_PATH}" ]; then
    echo "Backup file not found: ${BACKUP_PATH}"
    exit 1
fi

echo "Restoring from backup: ${BACKUP_FILE}"
echo "WARNING: This will overwrite the current database!"
read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Restore cancelled"
    exit 1
fi

# Stop the services
echo "Stopping services..."
docker-compose stop directus

# Restore the database
echo "Restoring database..."
if [[ "${BACKUP_FILE}" == *.gz ]]; then
    # Decompress and restore
    gunzip -c "${BACKUP_PATH}" | docker-compose exec -T postgres psql -U directus -d directus
else
    # Restore directly
    docker-compose exec -T postgres psql -U directus -d directus < "${BACKUP_PATH}"
fi

if [ $? -eq 0 ]; then
    echo "Database restored successfully"
    
    # Restart services
    echo "Restarting services..."
    docker-compose start directus
    
    echo "Restore completed successfully"
else
    echo "Restore failed"
    exit 1
fi
