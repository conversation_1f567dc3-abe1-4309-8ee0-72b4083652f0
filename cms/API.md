## 0. How to create fresh new account:
curl --location 'https://cms.copula.site/users' \
--header 'accept: application/json, text/plain, */*' \
--header 'content-type: application/json' \
--header 'Cookie: directus_session_token=pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO' \
--data-raw '{"email":"<EMAIL>","password":"123456", "role": "b9a6dffa-062b-4a24-b838-8b93d4a91147"}'


Please note that this token is admin token, which is different from the user token. This one will not expire.
We should avoid using admin token in most case, except for creating new user.


## 1. Login and get access_token

curl --location 'https://cms.copula.site/auth/login' \
--header 'Content-Type: application/json' \
--data-raw '{
  "email": "<EMAIL>",
  "password": "123456"
}'

sample response:
```
{
    "data": {
        "expires": **********,
        "refresh_token": "DYrP-6VWFd1agQUGYT-WdDt5EDgy0P4nJgdgBsHiPNQut45MlwZsTMhXdSjkOYob",
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjlmNGFhMmMxLWZjYTMtNDNiNy05OGZhLTM1OWVkNjU4OGMwMCIsInJvbGUiOiJiOWE2ZGZmYS0wNjJiLTRhMjQtYjgzOC04YjkzZDRhOTExNDciLCJhcHBfYWNjZXNzIjp0cnVlLCJhZG1pbl9hY2Nlc3MiOmZhbHNlLCJpYXQiOjE3NTkyNTA1MTQsImV4cCI6MTc2NzAyNjUxNCwiaXNzIjoiZGlyZWN0dXMifQ.H3d7w8lDTIGIZVrin3Egg4l93YGDm1_c_4eqNMN01P4"
    }
}
```
the access_token is valid for 90 days, enough for this demo app.


## 2. Get User Profile:
from JWT token, we already know the user_id (in this case, 9f4aa2c1-fca3-43b7-98fa-359ed6588c00), so we can get the user profile via its user_id:

curl --location  'https://cms.copula.site/items/UserProfile?filter[user][_eq]=9f4aa2c1-fca3-43b7-98fa-359ed6588c00' \
--header 'accept: application/json, text/plain, */*' \
--header 'Cookie: directus_session_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjlmNGFhMmMxLWZjYTMtNDNiNy05OGZhLTM1OWVkNjU4OGMwMCIsInJvbGUiOiJiOWE2ZGZmYS0wNjJiLTRhMjQtYjgzOC04YjkzZDRhOTExNDciLCJhcHBfYWNjZXNzIjp0cnVlLCJhZG1pbl9hY2Nlc3MiOmZhbHNlLCJpYXQiOjE3NTkyNTA1MTQsImV4cCI6MTc2NzAyNjUxNCwiaXNzIjoiZGlyZWN0dXMifQ.H3d7w8lDTIGIZVrin3Egg4l93YGDm1_c_4eqNMN01P4'


sample response:
```
{
    "data": [
        {
            "id": 3029,
            "status": "published",
            "user": "9f4aa2c1-fca3-43b7-98fa-359ed6588c00",
            "first_name": null,
            "last_name": null,
            "height": null,
            "marriage_status": null,
            "race": null,
            "age": null,
            "avatar": null,
            "desired_relationship": null,
            "essay": null,
            "smoke": null,
            "pets": null,
            "income": null,
            "education": null,
            "drink": null,
            "body_type": null,
            "gender": null,
            "sample_id": null,
            "city": null,
            "work": null,
            "language": null,
            "religion": null,
            "openness": null,
            "conscientiousness": null,
            "extraversion": null,
            "agreeableness": null,
            "neuroticism": null,
            "traditionalism": null,
            "conformity": null,
            "interests": null,
            "family_plan": null,
            "first_date": null,
            "friend_des_words": null,
            "images": []
        }
    ]
}
```

## 3. Update User Profile:
From API #2, we already know the user_profile_id (in this case, 2), so we can update the user profile via its user_profile_id:
Because we are using PATCH, we only need to provide the fields that we want to update.

curl --location --request PATCH 'https://cms.copula.site/items/UserProfile/3029' \
--header 'accept: application/json, text/plain, */*' \
--header 'content-type: application/json' \
--header 'Cookie: directus_session_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjlmNGFhMmMxLWZjYTMtNDNiNy05OGZhLTM1OWVkNjU4OGMwMCIsInJvbGUiOiJiOWE2ZGZmYS0wNjJiLTRhMjQtYjgzOC04YjkzZDRhOTExNDciLCJhcHBfYWNjZXNzIjp0cnVlLCJhZG1pbl9hY2Nlc3MiOmZhbHNlLCJpYXQiOjE3NTkyNTA1MTQsImV4cCI6MTc2NzAyNjUxNCwiaXNzIjoiZGlyZWN0dXMifQ.H3d7w8lDTIGIZVrin3Egg4l93YGDm1_c_4eqNMN01P4' \
--data '{"age":34,"first_name":"My first name","race":"asian"}'


sample response:

```
{
    "data": {
        "id": 2,
        "status": "published",
        "user": "e43b7fab-6622-47f2-b9e2-82f33c6514fe",
        "first_name": "My first name",
        "last_name": null,
        "height": null,
        "marriage_status": null,
        "race": "asian",
        "age": 34,
        "avatar": null,
        "desired_relationship": null,
        "essay": null,
        "smoke": null,
        "pets": null,
        "location": null,
        "income": null,
        "job": null,
        "education": null,
        "drink": null,
        "body_type": null,
        "gender": null,
        "images": []
    }
}
```

## 4. Upload Image:

### 4.1 To upload any image, we need to provide image location (in this case, "/Users/<USER>/Downloads/image1.jpg"):

curl --location 'https://cms.copula.site/files' \
--header 'accept: application/json, text/plain, */*' \
--header 'Cookie: directus_session_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjlmNGFhMmMxLWZjYTMtNDNiNy05OGZhLTM1OWVkNjU4OGMwMCIsInJvbGUiOiJiOWE2ZGZmYS0wNjJiLTRhMjQtYjgzOC04YjkzZDRhOTExNDciLCJhcHBfYWNjZXNzIjp0cnVlLCJhZG1pbl9hY2Nlc3MiOmZhbHNlLCJpYXQiOjE3NTkyNTA1MTQsImV4cCI6MTc2NzAyNjUxNCwiaXNzIjoiZGlyZWN0dXMifQ.H3d7w8lDTIGIZVrin3Egg4l93YGDm1_c_4eqNMN01P4' \
--form 'file=@"/Users/<USER>/Downloads/image1.jpg"'

sample response:

```
{
    "data": {
        "id": "3a155714-540d-4bc2-a0cb-7f2b04506fb6",
        "storage": "gcs",
        "filename_disk": "3a155714-540d-4bc2-a0cb-7f2b04506fb6.jpg",
        "filename_download": "image1.jpg",
        "title": "Image1",
        "type": "image/jpeg",
        "folder": null,
        "uploaded_by": "e43b7fab-6622-47f2-b9e2-82f33c6514fe",
        "created_on": "2025-09-04T17:38:41.346Z",
        "modified_by": null,
        "modified_on": "2025-09-04T17:38:41.544Z",
        "charset": null,
        "filesize": "91613",
        "width": 1280,
        "height": 800,
        "duration": null,
        "embed": null,
        "description": null,
        "location": null,
        "tags": null,
        "metadata": {},
        "focal_point_x": null,
        "focal_point_y": null,
        "tus_id": null,
        "tus_data": null,
        "uploaded_on": "2025-09-04T17:38:41.541Z"
    }
}
```

### 4.2 To access the uploaded image, we used its id (in this case, 3a155714-540d-4bc2-a0cb-7f2b04506fb6) to get the image:
https://cms.copula.site/assets/3a155714-540d-4bc2-a0cb-7f2b04506fb6 


### 4.3 To resize image, we can use the following API:
https://cms.copula.site/assets/3a155714-540d-4bc2-a0cb-7f2b04506fb6?width=400&height=600


## 5. Upload Avatar:
To upload Avatar, first, we need to upload the image using ##4, and then we can update the user profile with the image id:

curl --location --request PATCH 'https://cms.copula.site/items/UserProfile/2' \
--header 'accept: application/json, text/plain, */*' \
--header 'content-type: application/json' \
--header 'Cookie: directus_session_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjlmNGFhMmMxLWZjYTMtNDNiNy05OGZhLTM1OWVkNjU4OGMwMCIsInJvbGUiOiJiOWE2ZGZmYS0wNjJiLTRhMjQtYjgzOC04YjkzZDRhOTExNDciLCJhcHBfYWNjZXNzIjp0cnVlLCJhZG1pbl9hY2Nlc3MiOmZhbHNlLCJpYXQiOjE3NTkyNTA1MTQsImV4cCI6MTc2NzAyNjUxNCwiaXNzIjoiZGlyZWN0dXMifQ.H3d7w8lDTIGIZVrin3Egg4l93YGDm1_c_4eqNMN01P4' \
--data '{"avatar": "3a155714-540d-4bc2-a0cb-7f2b04506fb6"}'

sample response:
```
{
    "data": {
        "id": 2,
        "status": "published",
        "user": "e43b7fab-6622-47f2-b9e2-82f33c6514fe",
        "first_name": "My first name",
        "last_name": null,
        "height": null,
        "marriage_status": null,
        "race": "asian",
        "age": 34,
        "avatar": "001827ea-dfae-48a6-a352-cbd21b5e2117",
        "desired_relationship": null,
        "essay": null,
        "smoke": null,
        "pets": null,
        "location": null,
        "income": null,
        "job": null,
        "education": null,
        "drink": null,
        "body_type": null,
        "gender": null,
        "images": []
    }
}
```

## 6. Upload multiple images to User Profile:

First, we need to upload the images using ##4, 
Then we can update the user profile with the image ids:

curl --location --request PATCH 'https://cms.copula.site/items/UserProfile/2' \
--header 'accept: application/json, text/plain, */*' \
--header 'content-type: application/json' \
--header 'Cookie: directus_session_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjlmNGFhMmMxLWZjYTMtNDNiNy05OGZhLTM1OWVkNjU4OGMwMCIsInJvbGUiOiJiOWE2ZGZmYS0wNjJiLTRhMjQtYjgzOC04YjkzZDRhOTExNDciLCJhcHBfYWNjZXNzIjp0cnVlLCJhZG1pbl9hY2Nlc3MiOmZhbHNlLCJpYXQiOjE3NTkyNTA1MTQsImV4cCI6MTc2NzAyNjUxNCwiaXNzIjoiZGlyZWN0dXMifQ.H3d7w8lDTIGIZVrin3Egg4l93YGDm1_c_4eqNMN01P4' \
--data '{"images":{"create":[{"UserProfile_id":"2","directus_files_id":{"id":"5b0e1124-dccd-4be7-8007-e0e7f3843cbd"}},{"UserProfile_id":"2","directus_files_id":{"id":"95f84de1-e887-41d7-a9e4-c1d23cf759a6"}}]}}'

sample response:
```
{
    "data": {
        "id": 2,
        "status": "published",
        "user": "e43b7fab-6622-47f2-b9e2-82f33c6514fe",
        "first_name": "My first name",
        "last_name": null,
        "height": null,
        "marriage_status": null,
        "race": "asian",
        "age": 34,
        "avatar": "001827ea-dfae-48a6-a352-cbd21b5e2117",
        "desired_relationship": null,
        "essay": null,
        "smoke": null,
        "pets": null,
        "location": null,
        "income": null,
        "job": null,
        "education": null,
        "drink": null,
        "body_type": null,
        "gender": null,
        "images": [
            5,
            6
        ]
    }
}
```

as we see, the user profile now has two images, with ids 5 and 6, but we need to know its uuid to access the images, to do this, we can use the following API:

curl --location --globoff 'https://cms.copula.site/items/UserProfile?fields=images.directus_files_id.id&filter\[user\]\[_eq\]=e43b7fab-6622-47f2-b9e2-82f33c6514fe' \
--header 'accept: application/json, text/plain, */*' \
--header 'Cookie: directus_session_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjlmNGFhMmMxLWZjYTMtNDNiNy05OGZhLTM1OWVkNjU4OGMwMCIsInJvbGUiOiJiOWE2ZGZmYS0wNjJiLTRhMjQtYjgzOC04YjkzZDRhOTExNDciLCJhcHBfYWNjZXNzIjp0cnVlLCJhZG1pbl9hY2Nlc3MiOmZhbHNlLCJpYXQiOjE3NTkyNTA1MTQsImV4cCI6MTc2NzAyNjUxNCwiaXNzIjoiZGlyZWN0dXMifQ.H3d7w8lDTIGIZVrin3Egg4l93YGDm1_c_4eqNMN01P4'

sample response:
```
{
    "data": [
        {
            "images": [
                {
                    "directus_files_id": {
                        "id": "5b0e1124-dccd-4be7-8007-e0e7f3843cbd"
                    }
                },
                {
                    "directus_files_id": {
                        "id": "95f84de1-e887-41d7-a9e4-c1d23cf759a6"
                    }
                }
            ]
        }
    ]
}
```

actually, this API is the ##2 API, but we include a new parameter "images.directus_files_id.id" to get the image uuid.


## 7. Generate avatar image: 
curl --location 'https://controller.copula.site/generate-image-gemini' \
--header 'accept: application/json, text/plain, */*' \
--header 'content-type: application/json' \
--data-raw '{
       "prompt": "High quality photo of an asian woman, black suit, white shirt, long hair, soft smile, half body portrait, blur street background, detailed skin texture. keep the face look similar even with different gender.",
       "image_url": "https://cms.copula.site/assets/f3587fd8-b269-4d44-880d-6ab226f90fa0"
 }'

Please note that we are calling controller API, not cms API.

sample response:
```
{
  "success": true,
  "message": "Image generated successfully with Gemini",
  "data": {
    "generated_image_url": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/gemini_generated_20250930_153850_7e81425f.png",
    "generated_image_base64": null,
    "filename": "gemini_generated_20250930_153850_7e81425f.png",
    "bucket_path": "comfyui/common/gemini_generated_20250930_153850_7e81425f.png",
    "upload_error": null,
    "prompt_used": "High quality photo of an asian woman, black suit, white shirt, long hair, soft smile, half body portrait, blur street background, detailed skin texture. keep the face look similar even with different gender.",
    "source_image_url": "https://cms.copula.site/assets/f3587fd8-b269-4d44-880d-6ab226f90fa0"
  },
  "generation_time_seconds": 11.223
}
```

## 8. Create Synthetic Profile:

curl --location 'https://controller.copula.site/generate-image-gemini' \
--header 'accept: application/json, text/plain, */*' \
--header 'content-type: application/json' \
--data-raw '{
        "first_name": "Tony",
        "last_name": "Teo", 
        "height": 175,
		"race" : "asian",
        "age": 27,
        "marriage_status": null,
        "gender": "m",
        "drinks": "socially",
        "smokes": "missing",
        "education": "Undergraduate Degree (BA / BSc / BSSc / Other)",
        "work": "Professor",
        "city": "Hong Kong",
        "pets": "missing",
        "openness": "Imaginative",
        "conscientiousness": "Organised",
        "extraversion": "Reserved",
        "agreeableness": "Independent",
        "neuroticism": "Sensitive",
        "interests": "Chemistry Experiments, Bookbinding, Podcasting",
        "essay": "born in the philippines, grew up in a small town, went to cal for college and made the bay area my home. i hella love it here. the food, culture and people here are amazing!  im new to this online dating thing, so here we go...  i yelp everywhere i go. i enjoy trying new food; supporting the local hole in the wall places to the michelin starred restaurants.  my favorite meal is breakfast because i love bacon! followed by brunch- which is a close second because of bottomless mimosas.  i like people watching. i love hearing different languages although i only speak two, english and tagalog. i'd say three but it takes me too long to put sentences together in spanish. i understand a good deal of it though.  i want to travel the world someday. i've only been to a few countries and i want to add pins on a map of places i've been to.  the music in the bay runs through my veins. bay area slaps, hyphy music, i'm just out here gigging tryna function. you don't need tell me when to go because i'm in the buildin'... in all seriousness though i enjoy the local music because like the song says- \"when you get the chance to sit it out or dance...\" i choose dance! message me if you want to go dancing some time.  this also means i'm addicted to guilty pleasures like: americas best dance crew, so you think you can dance, as well as many other tv shows.  anyways i'm rambling on. it's hard to summarize oneself. if you want to know more read on or just ask i suppose.  cheers!",
        "traditionalism": "Progressive",
		"conformity" : "Autonomous"
    }'

Please note that we are calling controller API, not cms API.


Sample response:

```
{
  "success": true,
  "message": "Synthetic profile v2 created successfully",
  "data": {
    "synthetic_profile": {
      "first_name": "Tony",
      "last_name": "Teo",
      "height": 168,
      "race": "asian",
      "age": 27,
      "marriage_status": null,
      "gender": "f",
      "drinks": "socially",
      "smokes": "missing",
      "education": "Undergraduate Degree (BA / BSc / BSSc / Other)",
      "work": "Professor",
      "city": "Hong Kong",
      "pets": "missing",
      "openness": "Imaginative",
      "conscientiousness": "Organised",
      "extraversion": "Reserved",
      "agreeableness": "Independent",
      "neuroticism": "Sensitive",
      "interests": "Amateur Radio, Storytelling, Violin",
      "essay": "born in the philippines, grew up in a small town, went to cal for college and made the bay area my home. i hella love it here. the food, culture and people here are amazing!  im new to this online dating thing, so here we go...  i yelp everywhere i go. i enjoy trying new food; supporting the local hole in the wall places to the michelin starred restaurants.  my favorite meal is breakfast because i love bacon! followed by brunch- which is a close second because of bottomless mimosas.  i like people watching. i love hearing different languages although i only speak two, english and tagalog. i'd say three but it takes me too long to put sentences together in spanish. i understand a good deal of it though.  i want to travel the world someday. i've only been to a few countries and i want to add pins on a map of places i've been to.  the music in the bay runs through my veins. bay area slaps, hyphy music, i'm just out here gigging tryna function. you don't need tell me when to go because i'm in the buildin'... in all seriousness though i enjoy the local music because like the song says- \"when you get the chance to sit it out or dance...\" i choose dance! message me if you want to go dancing some time.  this also means i'm addicted to guilty pleasures like: americas best dance crew, so you think you can dance, as well as many other tv shows.  anyways i'm rambling on. it's hard to summarize oneself. if you want to know more read on or just ask i suppose.  cheers!",
      "traditionalism": "Progressive",
      "conformity": "Autonomous",
      "personality_tags": "Organised, Reserved, Independent"
    },
    "original_interests": [
      "Chemistry Experiments",
      "Bookbinding",
      "Podcasting"
    ],
    "synthetic_interests": [
      "Amateur Radio",
      "Storytelling",
      "Violin"
    ],
    "original_personality": [
      "Imaginative",
      "Organised",
      "Reserved",
      "Independent",
      "Sensitive"
    ],
    "selected_personality": [
      "Organised",
      "Reserved",
      "Independent"
    ]
  },
  "creation_time_seconds": 0.007
}
```

## 9. Get Matching Users: TBD