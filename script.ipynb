personality = ['Imaginative', 'Organised', 'Outgoing', 'Warm', 'Sensitive', 'Inquisitive', 'Adaptable', 'Friendly', 'Considerate', 'Perceptive', 'Conventional', 'Spontaneous', 'Reserved', 'Independent', 'Calm']

import re
def name_to_code(name):
    """
    Convert a hobby name to a code format (uppercase with underscores).
    
    Args:
        name (str): The hobby name
        
    Returns:
        str: The code format (e.g., "Open-minded" -> "OPEN_MINDED")
    """
    # Remove special characters and replace spaces/hyphens with underscores
    code = re.sub(r'[^\w\s-]', '', name)
    # Replace spaces and hyphens with underscores
    code = re.sub(r'[\s-]+', '_', code)
    # Convert to uppercase
    code = code.upper()
    # Remove leading/trailing underscores
    code = code.strip('_')
    return code



res = []
for x in personality:
    res.append(
        {"name": x, "code": name_to_code(x)}
    )
    
print(res)


import json
x = json.dumps(res)
x

import pandas as pd

df = pd.read_csv("sample.csv")

for column in df.columns:
    print(f"Column: {column}")
    print(f"Distinct values: {df[column].unique()}")
    print(f"Number of distinct values: {df[column].nunique()}")
    print("-" * 50)

import pandas as pd
df = pd.read_csv("new_200_interests.csv")
df.head()

res = []
for x in df['Interest']:
    res.append(
        {"name": x, "code": x}
    )
import json
b = json.dumps(res)
print(b)

