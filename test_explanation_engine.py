#!/usr/bin/env python3
"""
Test script for the new Match Explanation Engine v2

This script tests the optimized explanation engine that implements
the logic from ./script/example_explain.py
"""

import os
import sys
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path for imports
sys.path.append('.')

def test_explanation_engine():
    """Test the new explanation engine with sample data"""
    
    try:
        from match_explanation_engine import MatchExplanationEngine
        
        print("🧪 Testing Match Explanation Engine v2")
        print("=" * 50)
        
        # Initialize engine
        engine = MatchExplanationEngine()
        print("✅ Engine initialized successfully")
        
        # Sample synthetic profile
        synthetic_profile = {
            'first_name': 'You',
            'gender': 'm',
            'openness': 'Imaginative',
            'conscientiousness': 'Spontaneous', 
            'extraversion': 'Outgoing',
            'agreeableness': 'Independent',
            'neuroticism': 'Sensitive',
            'traditionalism': 'Traditional',
            'conformity': 'Compliant',
            'interests': 'Running, 3D Printing & Modeling, Wine tasting'
        }
        
        # Sample match profile
        match_profile = {
            'first_name': '<PERSON>',
            'name': '<PERSON>',
            'gender': 'm',
            'openness': 'Imaginative',
            'conscientiousness': 'Spontaneous',
            'extraversion': 'Outgoing', 
            'agreeableness': 'Independent',
            'neuroticism': 'Sensitive',
            'traditionalism': 'Progressive',
            'conformity': 'Compliant',
            'interests': 'Swimming, Traveling, Wine tasting'
        }
        
        # Sample user profile
        user_profile = {
            'first_name': 'Hana',
            'interests': 'Jiu-Jitsu, Video Games, Pizza'
        }
        
        print("\n📋 Test Data:")
        print(f"Synthetic Profile: {synthetic_profile['first_name']} ({synthetic_profile['gender']})")
        print(f"Match Profile: {match_profile['first_name']} ({match_profile['gender']})")
        print(f"User Profile: {user_profile['first_name']}")
        
        print("\n🎯 Testing Individual Components:")
        print("-" * 40)
        
        # Test 1: Ideal Type Introduction
        print("1. Ideal Type Introduction:")
        ideal_intro = engine.generate_ideal_type_intro(synthetic_profile)
        print(f"   {ideal_intro}")
        
        # Test 2: Interest Compatibility
        print("\n2. Interest Compatibility:")
        interest_compat = engine.generate_interest_compatibility(user_profile, synthetic_profile)
        print(f"   {interest_compat}")
        
        # Test 3: Spark Explanation
        print("\n3. AI-Powered Spark:")
        spark = engine.generate_spark_explanation(match_profile, synthetic_profile)
        print(f"   {spark}")
        
        # Test 4: Match Vibe Explanation
        print("\n4. Match Vibe Explanation:")
        vibe = engine.generate_match_vibe_explanation(match_profile, synthetic_profile)
        print(f"   {vibe}")
        
        # Test 5: First Date Idea
        print("\n5. First Date Idea:")
        date_idea = engine.generate_first_date_idea(match_profile, synthetic_profile)
        print(f"   {date_idea}")
        
        print("\n🎉 Testing Comprehensive Explanation:")
        print("-" * 40)
        
        # Test comprehensive explanation
        comprehensive = engine.generate_comprehensive_explanation(
            match=match_profile,
            synthetic_profile=synthetic_profile,
            user_profile=user_profile
        )
        
        print("Complete Explanation Components:")
        for key, value in comprehensive.items():
            print(f"  {key}: {value}")
        
        print("\n✅ All tests completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("Make sure match_explanation_engine.py is in the current directory")
        return False
    except Exception as e:
        print(f"❌ Test Error: {e}")
        return False

def test_main_function():
    """Test the main generate_match_explanation_v2 function"""
    
    try:
        from main import generate_match_explanation_v2
        
        print("\n🔧 Testing Main Function Integration:")
        print("-" * 40)
        
        # Sample data
        synthetic_profile = {
            'first_name': 'You',
            'gender': 'm',
            'openness': 'Imaginative',
            'conscientiousness': 'Spontaneous',
            'extraversion': 'Outgoing',
            'agreeableness': 'Independent', 
            'neuroticism': 'Sensitive',
            'traditionalism': 'Traditional',
            'conformity': 'Compliant',
            'interests': 'Running, Wine tasting, Reading'
        }
        
        match_profile = {
            'first_name': 'Sarah',
            'name': 'Sarah',
            'gender': 'f',
            'openness': 'Imaginative',
            'conscientiousness': 'Organised',
            'extraversion': 'Outgoing',
            'agreeableness': 'Warm',
            'neuroticism': 'Calm',
            'traditionalism': 'Traditional',
            'conformity': 'Compliant',
            'interests': 'Wine tasting, Reading, Yoga'
        }
        
        # Test main function
        explanation = generate_match_explanation_v2(
            synthetic_profile=synthetic_profile,
            match=match_profile
        )
        
        print(f"Generated Explanation:\n{explanation}")
        print(f"\nExplanation Length: {len(explanation)} characters")
        print(f"Word Count: {len(explanation.split())} words")
        
        print("\n✅ Main function test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Main function test error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Match Explanation Engine Tests")
    print("=" * 60)
    
    # Check if Gemini API key is available
    if not os.getenv('GEMINI_API_KEY'):
        print("⚠️  Warning: GEMINI_API_KEY not found in environment")
        print("   Some tests may fail without the API key")
    else:
        print("✅ GEMINI_API_KEY found in environment")
    
    print()
    
    # Run tests
    engine_test = test_explanation_engine()
    main_test = test_main_function()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"  Engine Test: {'✅ PASSED' if engine_test else '❌ FAILED'}")
    print(f"  Main Function Test: {'✅ PASSED' if main_test else '❌ FAILED'}")
    
    if engine_test and main_test:
        print("\n🎉 All tests passed! The explanation engine is ready to use.")
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")
