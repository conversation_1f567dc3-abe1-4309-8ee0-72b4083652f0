"""
Optimized Match Explanation Engine for v2 Matching Logic

This module contains the logic for generating AI-powered match explanations
based on personality, values, and interests compatibility.
"""

import os
import json
import random
import pathlib
import logging
import google.generativeai as genai
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

# Personality and Values Fact Sheets
PERSONALITY_FACT_SHEET = """
OPENNESS
If score == 0: Practical, realistic, consistent, familiar
If score == 1: Imaginative, curious, creative, unconventional, explorer

CONSCIENTIOUSNESS  
If score == 0: Spontaneous, unstructured, flexible, adaptable
If score == 1: Organized, disciplined, thorough, prepared

EXTRAVERSION
If score == 0: Self-sufficient, reserved, introspective, quiet, solitary
If score == 1: Outgoing, sociable, enthusiastic, expressive, lively

AGREEABLENESS
If score == 0: Assertive, self-reliant, direct, questioning
If score == 1: Friendly, cooperative, gentle, altruistic, polite

NEUROTICISM
If score == 0: Resilient, composed, unreactive, stable, even-tempered
If score == 1: Sensitive, self-conscious, expressive, attuned, passionate
"""

VALUES_FACT_SHEET = """
TRADITIONALISM
If score == 0 (Progressive): Modern, progress, dynamic, receptive, self-defined, open-minded
If score == 1 (Traditional): Traditional, conventional, respectful, "old soul", grounded

CONFORMITY
If score == 0 (Autonomous): Self-direction, autonomy, freedom, offbeat, individualistic  
If score == 1 (Compliant): Obedient, compliant, rule-follower, mainstream, respectful
"""


class MatchExplanationEngine:
    """Optimized engine for generating match explanations using Gemini AI"""
    
    def __init__(self):
        """Initialize the explanation engine with Gemini API"""
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        if not self.gemini_api_key:
            raise ValueError("GEMINI_API_KEY environment variable is not set")
        genai.configure(api_key=self.gemini_api_key)
        self.model = genai.GenerativeModel("gemini-2.0-flash-exp")
        
        # Load category mapping
        self.category_to_interests = self._load_category_mapping()
    
    def _load_category_mapping(self) -> Dict:
        """Load interest category mapping"""
        try:
            category_file = pathlib.Path(__file__).parent / "files" / "category_to_interests.json"
            with open(category_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"Could not load category mapping: {e}")
            return {}
    
    def _map_personality_traits(self, personality_scores: Dict) -> List[str]:
        """Map personality scores to trait keywords"""
        personality_mapping = {
            'openness': {1: 'imaginative', 0: 'conventional'},
            'conscientiousness': {1: 'organised', 0: 'spontaneous'},
            'extraversion': {1: 'outgoing', 0: 'reserved'},
            'agreeableness': {1: 'warm', 0: 'independent'},
            'neuroticism': {1: 'sensitive', 0: 'calm'}
        }
        
        traits = []
        for trait, score in personality_scores.items():
            trait_lower = trait.lower()
            if trait_lower in personality_mapping and score in personality_mapping[trait_lower]:
                traits.append(personality_mapping[trait_lower][score])
        return traits
    
    def _map_values_traits(self, traditionalism: str, conformity: str) -> List[str]:
        """Map values to trait keywords"""
        values_mapping = {
            'traditionalism': {'Traditional': 'traditional', 'Progressive': 'progressive'},
            'conformity': {'Compliant': 'compliant', 'Autonomous': 'autonomous'}
        }
        
        traits = []
        if traditionalism in values_mapping['traditionalism']:
            traits.append(values_mapping['traditionalism'][traditionalism])
        if conformity in values_mapping['conformity']:
            traits.append(values_mapping['conformity'][conformity])
        return traits
    
    def _find_shared_traits(self, synthetic_profile: Dict, match: Dict) -> Dict:
        """Find shared personality traits, values, and interests"""
        shared = {
            'personality': {},
            'values': [],
            'interests': []
        }
        
        # Shared personality traits
        synth_personality = self._extract_personality_scores(synthetic_profile)
        match_personality = self._extract_personality_scores(match)
        
        for trait, score in synth_personality.items():
            if trait in match_personality and match_personality[trait] == score:
                shared['personality'][trait] = score
        
        # Shared values
        synth_traditionalism = synthetic_profile.get('traditionalism', '')
        synth_conformity = synthetic_profile.get('conformity', '')
        match_traditionalism = match.get('traditionalism', '')
        match_conformity = match.get('conformity', '')
        
        if synth_traditionalism == match_traditionalism:
            shared['values'].append(f"traditionalism_{synth_traditionalism}")
        if synth_conformity == match_conformity:
            shared['values'].append(f"conformity_{synth_conformity}")
        
        # Shared interests
        synth_interests = self._extract_interests(synthetic_profile)
        match_interests = self._extract_interests(match)
        shared['interests'] = [interest for interest in synth_interests if interest in match_interests]
        
        return shared
    
    def _extract_personality_scores(self, profile: Dict) -> Dict:
        """Extract personality scores from profile"""
        personality_fields = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism']
        scores = {}
        
        for field in personality_fields:
            if field in profile:
                # Handle both text values and numeric scores
                value = profile[field]
                if isinstance(value, str):
                    # Map text values to binary scores
                    if value.lower() in ['imaginative', 'organised', 'outgoing', 'warm', 'sensitive']:
                        scores[field] = 1
                    elif value.lower() in ['conventional', 'spontaneous', 'reserved', 'independent', 'calm']:
                        scores[field] = 0
                elif isinstance(value, (int, float)):
                    scores[field] = 1 if value > 0.5 else 0
        
        return scores
    
    def _extract_interests(self, profile: Dict) -> List[str]:
        """Extract interests from profile"""
        interests = profile.get('interests', '')
        if isinstance(interests, str):
            return [interest.strip() for interest in interests.split(',') if interest.strip()]
        elif isinstance(interests, list):
            return interests
        return []

    def generate_ideal_type_intro(self, synthetic_profile: Dict) -> str:
        """Generate introduction of ideal type based on personality and values"""
        try:
            synth_personality = self._extract_personality_scores(synthetic_profile)
            synth_traditionalism = synthetic_profile.get('traditionalism', '')
            synth_conformity = synthetic_profile.get('conformity', '')
            synth_gender = synthetic_profile.get('gender', 'they')

            # Use appropriate pronouns
            pronoun = 'he' if synth_gender.lower() == 'm' else 'she' if synth_gender.lower() == 'f' else 'they'

            prompt = f"""
            As a dating AI assistant, write a short introduction of the user's ideal partner.
            The introduction should include personality traits {synth_personality} and values (traditionalism: {synth_traditionalism}, conformity: {synth_conformity}).
            Use appropriate pronoun ({pronoun}) based on the gender.
            Start with 'Your type is' and use keywords from the technical specifications.
            Do not mention scores or trait names directly.
            Technical specifications: {PERSONALITY_FACT_SHEET}, {VALUES_FACT_SHEET}
            Limit to 2-3 sentences.
            """

            response = self.model.generate_content(prompt)
            return response.text.strip()

        except Exception as e:
            logger.error(f"Error generating ideal type intro: {str(e)}")
            return "Your ideal type is someone who complements your personality and values."

    def generate_interest_compatibility(self, user_profile: Dict, synthetic_profile: Dict) -> str:
        """Generate interest-based compatibility explanation"""
        try:
            synth_interests = self._extract_interests(synthetic_profile)
            synth_gender = synthetic_profile.get('gender', 'they')

            # Find interest categories
            category_list = []
            for interest in synth_interests:
                for category, interests in self.category_to_interests.items():
                    if interest in interests:
                        category_list.append(category)
                        break

            pronoun = 'he' if synth_gender.lower() == 'm' else 'she' if synth_gender.lower() == 'f' else 'they'

            prompt = f"""
            As a dating AI assistant, write a short introduction about shared interests.
            The interest categories are: {category_list}
            Combine these categories to show how you'll bond together.
            Use appropriate pronoun ({pronoun}).
            Sample: "As you both love exploring the world and creating art, you'll have great times together."
            Limit to 1-2 sentences.
            """

            response = self.model.generate_content(prompt)
            return response.text.strip()

        except Exception as e:
            logger.error(f"Error generating interest compatibility: {str(e)}")
            return "You share similar interests that will help you bond together."

    def generate_spark_explanation(self, match: Dict, synthetic_profile: Dict) -> str:
        """Generate AI-powered spark explanation based on shared traits"""
        try:
            shared = self._find_shared_traits(synthetic_profile, match)
            match_gender = match.get('gender', 'they')

            # Combine all shared traits
            personality_traits = self._map_personality_traits(shared['personality'])
            values_traits = self._map_values_traits(
                synthetic_profile.get('traditionalism', ''),
                synthetic_profile.get('conformity', '')
            )
            combined_tags = personality_traits + values_traits + shared['interests']

            # Select up to 3 random tags
            selected_tags = random.sample(combined_tags, min(3, len(combined_tags))) if combined_tags else []

            pronoun = 'he' if match_gender.lower() == 'm' else 'she' if match_gender.lower() == 'f' else 'they'

            prompt = f"""
            As a dating consultant, explain why this candidate is suitable using these shared traits: {selected_tags}
            Use appropriate pronoun ({pronoun}).
            Start with 'Just as your type' and limit to 1 sentence with no more than 30 words.
            Use 'you' or 'your' for interactive expression.
            Sample: 'Just as your type, she is an introvert who thinks deeply and loves exploring the world.'
            """

            response = self.model.generate_content(prompt)
            return response.text.strip()

        except Exception as e:
            logger.error(f"Error generating spark explanation: {str(e)}")
            return "Just as your type, this person shares your core values and interests."

    def generate_match_vibe_explanation(self, match: Dict, synthetic_profile: Dict) -> str:
        """Generate detailed explanation of match's vibe and compatibility"""
        try:
            match_name = match.get('name', 'This person')
            shared = self._find_shared_traits(synthetic_profile, match)

            prompt = f"""
            As a dating consultant, explain why {match_name} is suitable based on shared traits.
            Shared personality: {shared['personality']}
            Shared values: {shared['values']}
            Shared interests: {shared['interests']}

            Start with 'When {match_name} is in a relationship, {match_name} tends to' and explain their relationship style.
            Use keywords from technical specifications but don't mention scores or trait names.
            Limit to 4-5 sentences.
            When mentioning the user, use 'You'.

            Technical specifications: {PERSONALITY_FACT_SHEET}, {VALUES_FACT_SHEET}
            """

            response = self.model.generate_content(prompt)
            return response.text.strip()

        except Exception as e:
            logger.error(f"Error generating match vibe explanation: {str(e)}")
            return f"When {match.get('name', 'this person')} is in a relationship, they bring warmth and compatibility to the connection."

    def generate_first_date_idea(self, match: Dict, synthetic_profile: Dict) -> str:
        """Generate first date idea based on shared interests"""
        try:
            shared = self._find_shared_traits(synthetic_profile, match)
            shared_interests = shared['interests']

            prompt = f"""
            As a dating consultant, suggest a first date idea based on shared interests: {shared_interests}
            Start with 'The first date could be' and limit to 2-3 sentences.
            Use 'you' or 'your' for interactive expression.
            Sample: 'The first date could be exploring a local art gallery, followed by coffee to discuss your favorite pieces.'
            """

            response = self.model.generate_content(prompt)
            return response.text.strip()

        except Exception as e:
            logger.error(f"Error generating first date idea: {str(e)}")
            return "The first date could be a casual coffee meeting to get to know each other better."

    def generate_comprehensive_explanation(self, match: Dict, synthetic_profile: Dict, user_profile: Dict = None) -> Dict[str, str]:
        """Generate all explanation components for a match"""
        try:
            explanations = {
                'ideal_type_intro': self.generate_ideal_type_intro(synthetic_profile),
                'spark_explanation': self.generate_spark_explanation(match, synthetic_profile),
                'match_vibe': self.generate_match_vibe_explanation(match, synthetic_profile),
                'first_date_idea': self.generate_first_date_idea(match, synthetic_profile)
            }

            # Add interest compatibility if user profile is provided
            if user_profile:
                explanations['interest_compatibility'] = self.generate_interest_compatibility(user_profile, synthetic_profile)

            return explanations

        except Exception as e:
            logger.error(f"Error generating comprehensive explanation: {str(e)}")
            return {
                'ideal_type_intro': "Your ideal type complements your personality perfectly.",
                'spark_explanation': "This person shares your core values and interests.",
                'match_vibe': "This match brings great compatibility to a relationship.",
                'first_date_idea': "A casual coffee date would be perfect to start.",
                'interest_compatibility': "You share interests that will help you bond."
            }
