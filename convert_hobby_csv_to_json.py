#!/usr/bin/env python3
"""
<PERSON>ript to convert hobby.csv file to JSON format.
Reads each line from files/hobby.csv and converts it to JSON format with 'name' and 'code' fields.
"""

import csv
import json
import re
from pathlib import Path


def name_to_code(name):
    """
    Convert a hobby name to a code format (uppercase with underscores).
    
    Args:
        name (str): The hobby name
        
    Returns:
        str: The code format (e.g., "Open-minded" -> "OPEN_MINDED")
    """
    # Remove special characters and replace spaces/hyphens with underscores
    code = re.sub(r'[^\w\s-]', '', name)
    # Replace spaces and hyphens with underscores
    code = re.sub(r'[\s-]+', '_', code)
    # Convert to uppercase
    code = code.upper()
    # Remove leading/trailing underscores
    code = code.strip('_')
    return code


def convert_csv_to_json():
    """
    Read hobby.csv file and convert to JSON format.
    """
    input_file = Path("files/hobby.csv")
    output_file = Path("files/hobby.json")
    
    # Check if input file exists
    if not input_file.exists():
        print(f"Error: Input file {input_file} not found!")
        return
    
    hobbies = []
    
    try:
        # Read the CSV file
        with open(input_file, 'r', encoding='utf-8') as file:
            # Read each line as a hobby name (no CSV headers expected)
            for line_num, line in enumerate(file, 1):
                hobby_name = line.strip()
                
                # Skip empty lines
                if not hobby_name:
                    continue
                
                # Create the JSON object
                hobby_obj = {
                    "name": hobby_name,
                    "code": name_to_code(hobby_name)
                }
                
                hobbies.append(hobby_obj)
                print(f"Processed line {line_num}: {hobby_name} -> {hobby_obj['code']}")
        
        # Write to JSON file
        with open(output_file, 'w', encoding='utf-8') as file:
            json.dump(hobbies, file, indent=2, ensure_ascii=False)
        
        print(f"\nSuccessfully converted {len(hobbies)} hobbies to {output_file}")
        print(f"Output file: {output_file.absolute()}")
        
    except Exception as e:
        print(f"Error processing file: {e}")


if __name__ == "__main__":
    convert_csv_to_json()
