#!/usr/bin/env python3
"""
Profile Update Script

This script updates existing user profiles with correct data from new_data.csv.
It will NOT touch the avatar field - only updates profile data fields.

Usage:
    python3 update_profiles_from_csv.py [--start N] [--limit N] [--csv path] [--test]
"""

import argparse
import csv
import json
import os
import requests
import sys
import time

# Configuration
BASE_URL = "https://cms.copula.site"
SESSION_TOKEN = "pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO"
DEFAULT_CSV_PATH = "new_data.csv"

# Headers for API requests
HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'Cookie': f'directus_session_token={SESSION_TOKEN}'
}

HEADERS_JSON = {
    'accept': 'application/json, text/plain, */*',
    'content-type': 'application/json',
    'Cookie': f'directus_session_token={SESSION_TOKEN}'
}

def get_user_profile_by_sample_id(sample_id):
    """
    Retrieve user profile by sample_id using the filter API
    """
    url = f"{BASE_URL}/items/UserProfile"
    params = {
        'filter[sample_id][_eq]': sample_id
    }
    
    try:
        response = requests.get(url, headers=HEADERS, params=params)
        response.raise_for_status()
        
        data = response.json()
        if data.get('data') and len(data['data']) > 0:
            return data['data'][0]
        else:
            print(f"❌ No profile found for sample_id: {sample_id}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error retrieving profile for sample_id {sample_id}: {e}")
        return None

def map_csv_to_profile_data(row):
    """
    Map CSV row data to profile update data (excluding avatar field)
    
    Args:
        row (dict): CSV row data
        
    Returns:
        dict: Mapped profile data
    """
    # Field mappings based on new_data.csv structure
    profile_data = {}
    
    # Direct mappings for special fields
    if 'id' in row and row['id']:
        profile_data['sample_id'] = int(row['id'])
    
    if 'height_cm' in row and row['height_cm']:
        profile_data['height'] = float(row['height_cm'])
    
    # Other field mappings - CSV field name -> API field name
    field_mappings = {
        'first_name': 'first_name',
        'last_name': 'last_name', 
        'age': 'age',
        'race': 'race',
        'gender': 'gender',
        'education': 'education',
        'work': 'work',  # CSV field 'work' -> API field 'work'
        'city': 'city',  # CSV field 'city' -> API field 'city'
        'language': 'language',
        'religion': 'religion',
        'desired_relationship': 'desired_relationship',
        'smokes': 'smoke',  # CSV field 'smokes' -> API field 'smoke'
        'pets': 'pets',
        'income': 'income',
        'drinks': 'drink',  # CSV field 'drinks' -> API field 'drink'
        'openness': 'openness',
        'conscientiousness': 'conscientiousness',
        'extraversion': 'extraversion',
        'agreeableness': 'agreeableness',
        'neuroticism': 'neuroticism',
        'traditionalism': 'traditionalism',
        'conformity': 'conformity',
        'interests': 'interests',
        'family_plan': 'family_plan'
    }
    
    for csv_field, api_field in field_mappings.items():
        if csv_field in row and row[csv_field] and row[csv_field] != '':
            value = row[csv_field]
            
            # Convert numeric fields
            if api_field in ['age', 'income'] and value:
                try:
                    profile_data[api_field] = float(value)
                except ValueError:
                    print(f"⚠️  Could not convert {api_field} value '{value}' to number")
                    profile_data[api_field] = value
            else:
                profile_data[api_field] = value
    
    return profile_data

def update_profile_data(profile_id, profile_data):
    """
    Update user profile with new data (excluding avatar)
    """
    url = f"{BASE_URL}/items/UserProfile/{profile_id}"
    
    # Remove avatar field if it exists to ensure we don't touch it
    if 'avatar' in profile_data:
        del profile_data['avatar']
    
    try:
        response = requests.patch(url, headers=HEADERS_JSON, json=profile_data)
        response.raise_for_status()
        
        print(f"✅ Profile {profile_id} updated successfully")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Error updating profile {profile_id}: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"Response: {e.response.text}")
        return False

def update_user_profile_from_csv(csv_row):
    """
    Update user profile with correct data from CSV
    Returns: (success, skip_reason)
    """
    sample_id = csv_row['id']
    first_name = csv_row['first_name']
    last_name = csv_row['last_name']
    
    print(f"\n🔄 Updating {first_name} {last_name} (ID: {sample_id})")
    
    # Step 1: Get user profile
    profile = get_user_profile_by_sample_id(sample_id)
    if not profile:
        return False, "Profile not found"
    
    profile_id = profile['id']
    
    # Step 2: Map CSV data to profile data
    profile_data = map_csv_to_profile_data(csv_row)
    
    # Step 3: Update profile with CSV data (excluding avatar)
    print(f"🔄 Updating profile {profile_id} with CSV data")
    success = update_profile_data(profile_id, profile_data)
    
    if success:
        print(f"✅ Successfully updated {first_name} {last_name}")
        return True, "Success"
    else:
        return False, "Profile update failed"

def main():
    parser = argparse.ArgumentParser(description='Update user profiles with correct data from CSV')
    parser.add_argument('--start', type=int, default=0, help='Start from record number (0-based)')
    parser.add_argument('--limit', type=int, help='Limit number of records to process')
    parser.add_argument('--csv', default=DEFAULT_CSV_PATH, help='Path to CSV file')
    parser.add_argument('--test', action='store_true', help='Test mode - process only first record')
    
    args = parser.parse_args()
    
    # Test mode overrides
    if args.test:
        args.start = 0
        args.limit = 1
        print("🧪 Running in TEST MODE - processing only first record")
    
    csv_path = args.csv
    if not os.path.exists(csv_path):
        print(f"❌ CSV file not found: {csv_path}")
        sys.exit(1)
    
    print(f"🔄 Updating profiles with correct CSV data...")
    print(f"📊 Reading CSV file: {csv_path}")
    print(f"🌐 API Base URL: {BASE_URL}")
    print(f"⚠️  NOTE: Avatar fields will NOT be touched")
    
    # Read CSV file
    records = []
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        records = list(reader)
    
    total_records = len(records)
    print(f"📋 Total records in CSV: {total_records}")
    
    # Apply start and limit
    start_idx = args.start
    if args.limit:
        end_idx = min(start_idx + args.limit, total_records)
    else:
        end_idx = total_records
    
    records_to_process = records[start_idx:end_idx]
    print(f"🎯 Processing records {start_idx} to {end_idx-1} ({len(records_to_process)} records)")
    
    # Process records
    success_count = 0
    error_count = 0
    
    for i, record in enumerate(records_to_process, start=start_idx):
        print(f"\n{'='*60}")
        print(f"📝 Record {i+1}/{total_records}")
        
        success, reason = update_user_profile_from_csv(record)
        
        if success:
            success_count += 1
        else:
            error_count += 1
            print(f"❌ Failed: {reason}")
        
        # Rate limiting - 0.5 second delay
        if i < end_idx - 1:  # Don't delay after the last record
            print("⏱️  Waiting 0.5 seconds...")
            time.sleep(0.5)
    
    # Final summary
    print(f"\n{'='*60}")
    print("📊 FINAL SUMMARY")
    print(f"✅ Successfully updated: {success_count}")
    print(f"❌ Failed: {error_count}")
    print(f"📋 Total processed: {len(records_to_process)}")
    
    success_rate = (success_count / len(records_to_process)) * 100 if records_to_process else 0
    print(f"📈 Success rate: {success_rate:.1f}%")

if __name__ == "__main__":
    main()
