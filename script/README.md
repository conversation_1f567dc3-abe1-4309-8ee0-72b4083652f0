# User Profile Creator Script

This script implements the strategy outlined in `strategy.md` to create users and update their profiles using data from CSV files.

## Overview

The script performs the following operations for each CSV record:

1. **Create User** - Creates a new user via API 1 (`POST /users`)
2. **Get Profile** - Retrieves the user profile via API 2 (`GET /items/UserProfile`)
3. **Update Profile** - Updates the profile with CSV data via API 3 (`PATCH /items/UserProfile/{id}`)

## Files

- `user_profile_creator.py` - Main script for creating and updating profiles
- `verify_profile.py` - Verification script to check created profiles
- `avatar_uploader.py` - Script for uploading avatar images to user profiles
- `verify_avatars.py` - Verification script to check uploaded avatars
- `reupload_avatars.py` - Script to re-upload avatars with correct MIME types
- `verify_mime_types.py` - Verification script to check avatar MIME types
- `update_profiles_from_csv.py` - Script to update profiles with correct data from new_data.csv
- `strategy.md` - Original strategy document with API details

## CSV Data Mapping

The script maps CSV fields to API fields according to the strategy:

| CSV Field | API Field | Notes |
|-----------|-----------|-------|
| `id` | `sample_id` | As specified in strategy |
| `essay0` | `essay` | As specified in strategy |
| `height_cm` | `height` | As specified in strategy |
| `first_name` | `first_name` | Direct mapping |
| `last_name` | `last_name` | Direct mapping |
| `age` | `age` | Direct mapping |
| `race` | `race` | Direct mapping |
| `gender` | `gender` | Direct mapping |
| `education` | `education` | Direct mapping |
| `job` | `work` | Direct mapping |
| `location` | `city` | Direct mapping |
| `desired_relationship` | `desired_relationship` | Direct mapping |
| `smokes` | `smoke` | Direct mapping |
| `pets` | `pets` | Direct mapping |
| `income` | `income` | Direct mapping |
| `drinks` | `drink` | Direct mapping |
| `body_type` | `body_type` | Direct mapping |
| `openness` | `openness` | Direct mapping |
| `conscientiousness` | `conscientiousness` | Direct mapping |
| `extraversion` | `extraversion` | Direct mapping |
| `agreeableness` | `agreeableness` | Direct mapping |
| `neuroticism` | `neuroticism` | Direct mapping |
| `interests` | `interests` | Direct mapping |
| `family_plan` | `family_plan` | Direct mapping |

**Ignored fields** (as per strategy): `file_name`, `image_source`

## Usage

### Basic Usage (Test Mode)

Test with the first record only:

```bash
python3 user_profile_creator.py
```

### Process All Records

Process all records in the CSV file:

```bash
python3 user_profile_creator.py --all
```

### Advanced Options

```bash
# Start from a specific record (0-based index)
python3 user_profile_creator.py --all --start 10

# Process only a limited number of records
python3 user_profile_creator.py --all --limit 5

# Start from record 10 and process 5 records
python3 user_profile_creator.py --all --start 10 --limit 5

# Use a specific CSV file
python3 user_profile_creator.py --csv /path/to/your/file.csv

# Get help
python3 user_profile_creator.py --help
```

### Verification

To verify that profiles were created correctly:

```bash
python3 verify_profile.py
```

## Avatar Upload Process

After creating user profiles, you can upload avatar images using the avatar upload scripts.

### Avatar Upload Script (`avatar_uploader.py`)

Uploads avatar images for user profiles based on the `file_name` field in the CSV.

**Features:**
- Checks if user profile already has an avatar (skips if exists)
- Uploads image files to Directus CMS via POST /files API
- Updates user profiles with avatar image ID via PATCH /items/UserProfile/{id} API
- Rate limiting with 0.5-second delays between API calls
- Comprehensive error handling and progress tracking

**Usage:**
```bash
# Test with first record only (recommended first)
python3 avatar_uploader.py --test

# Process all records starting from record 2 (skip first 2)
python3 avatar_uploader.py --start 2

# Process specific range
python3 avatar_uploader.py --start 10 --limit 50

# Use custom CSV file
python3 avatar_uploader.py --csv path/to/your/file.csv
```

### Avatar Verification Script (`verify_avatars.py`)

Verifies that avatar images have been uploaded correctly.

**Usage:**
```bash
# Check first 10 records (default)
python3 verify_avatars.py

# Check first 50 records
python3 verify_avatars.py --limit 50
```

### Avatar Re-upload Script (`reupload_avatars.py`)

Re-uploads avatar images with correct MIME types. This script was created to fix the issue where images were initially uploaded as `text/plain` instead of proper image MIME types.

**Features:**
- Re-uploads images with correct MIME types (image/png, image/jpeg, etc.)
- Deletes old incorrectly-typed images after successful upload
- Updates user profiles with new avatar IDs
- Same rate limiting and error handling as original uploader

**Usage:**
```bash
# Test with first record only (recommended first)
python3 reupload_avatars.py --test

# Re-upload all avatars
python3 reupload_avatars.py

# Re-upload specific range
python3 reupload_avatars.py --start 10 --limit 50
```

### MIME Type Verification Script (`verify_mime_types.py`)

Verifies that uploaded avatar images have correct MIME types.

**Usage:**
```bash
# Check first 10 records (default)
python3 verify_mime_types.py

# Check first 50 records
python3 verify_mime_types.py --limit 50
```

### Profile Data Update Script (`update_profiles_from_csv.py`)

Updates existing user profiles with correct data from new_data.csv. This script was created to fix incorrect field mappings that caused profiles to have wrong data.

**Features:**
- Updates all profile fields with correct data from new_data.csv
- Does NOT touch avatar fields (avatars remain intact)
- Fixes field mapping issues (education, work, city, etc.)
- Same rate limiting and error handling as other scripts

**Usage:**
```bash
# Test with first record only (recommended first)
python3 update_profiles_from_csv.py --test

# Update all profiles
python3 update_profiles_from_csv.py

# Update specific range
python3 update_profiles_from_csv.py --start 10 --limit 50
```

### Avatar Upload Workflow

The avatar upload process follows this workflow:

1. **Check Profile**: Retrieve user profile by `sample_id` from CSV
2. **Check Existing Avatar**: Skip if profile already has an avatar
3. **Locate Image File**: Find image file in `/files/face_photos_2k/` using `file_name` from CSV
4. **Upload Image**: Upload image file to Directus CMS and get image ID
5. **Update Profile**: Update user profile with the new avatar image ID

**Image File Mapping:**
- CSV `id` field maps to profile `sample_id`
- CSV `file_name` field specifies the image filename
- Images are located in `/Users/<USER>/Documents/hailstone/copula_backend/files/face_photos_2k/`

## CSV File Selection

The script automatically selects the appropriate CSV file:

1. **First choice**: `new_data.csv` (if it has the `essay0` field)
2. **Fallback**: `../files/dating_app_sample_100_essay_tag.csv`

You can override this with the `--csv` parameter.

## Configuration

The script uses these configuration values (defined at the top of the file):

```python
BASE_URL = "https://cms.copula.site"
SESSION_TOKEN = "pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO"
ROLE_ID = "b9a6dffa-062b-4a24-b838-8b93d4a91147"
DEFAULT_PASSWORD = "123456"
```

## Error Handling

The script includes comprehensive error handling:

- **Duplicate emails**: Skips records if user email already exists
- **API failures**: Logs errors and continues with next record
- **Missing data**: Handles missing or invalid CSV fields gracefully
- **Rate limiting**: Includes delays between API calls

## Output

The script provides detailed output including:

- Progress indicators for each step
- Success/failure status for each record
- Final statistics (total processed, successful, failed, success rate)
- Detailed error messages for debugging

## Example Output

```
🚀 User Profile Creator Script
Based on strategy.md requirements

📋 Using dating_app_sample_100_essay_tag.csv
🧪 Testing with first record only...
📁 Reading CSV file: /path/to/file.csv

============================================================
Processing record 1: ID 11462 (#1)
============================================================
✓ Created user: <EMAIL> with ID: e8cd3eee-978a-4267-882c-53d88b5045c8
✓ Found user profile ID: 5 for user: e8cd3eee-978a-4267-882c-53d88b5045c8
📝 Profile data to update: {...}
✓ Updated profile ID: 5
✅ Successfully processed record 1

📊 Final Statistics:
  Total processed: 1
  Successful: 1
  Failed: 0
  Success rate: 100.0%

🎉 Script completed!
```

## Testing

The script has been tested with:

- ✅ First record (ID: 11462) - Successfully created and updated
- ✅ Second record (ID: 12327) - Successfully created and updated
- ✅ Duplicate handling - Correctly skips existing users
- ✅ Field mapping - All CSV fields correctly mapped to API fields
- ✅ Error handling - Gracefully handles API errors

## Requirements

- Python 3.6+
- `requests` library
- Valid session token and API access

## Security Notes

- Session token is hardcoded in the script (consider using environment variables for production)
- Default password is used for all created users
- API calls are made over HTTPS

## Next Steps

After testing with the first record, you can:

1. Run with `--all` to process all records
2. Use `--start` and `--limit` for batch processing
3. Monitor the output for any failures
4. Use the verification script to confirm data integrity
