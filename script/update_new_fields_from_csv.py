#!/usr/bin/env python3
"""
New Fields Update Script

This script updates existing user profiles with the new friend_des_words and first_date fields from new_data.csv.
It only updates these two specific fields and leaves all other profile data unchanged.

Usage:
    python3 update_new_fields_from_csv.py [--start N] [--limit N] [--csv path] [--test]
"""

import argparse
import csv
import json
import os
import requests
import sys
import time

# Configuration
BASE_URL = "https://cms.copula.site"
SESSION_TOKEN = "pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO"
DEFAULT_CSV_PATH = "new_data.csv"

# Headers for API requests
HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'Cookie': f'directus_session_token={SESSION_TOKEN}'
}

HEADERS_JSON = {
    'accept': 'application/json, text/plain, */*',
    'content-type': 'application/json',
    'Cookie': f'directus_session_token={SESSION_TOKEN}'
}

def get_user_profile_by_sample_id(sample_id):
    """
    Retrieve user profile by sample_id using the filter API
    """
    url = f"{BASE_URL}/items/UserProfile"
    params = {
        'filter[sample_id][_eq]': sample_id
    }
    
    try:
        response = requests.get(url, headers=HEADERS, params=params)
        response.raise_for_status()
        
        data = response.json()
        if data.get('data') and len(data['data']) > 0:
            return data['data'][0]
        else:
            print(f"❌ No profile found for sample_id: {sample_id}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error retrieving profile for sample_id {sample_id}: {e}")
        return None

def extract_new_fields_data(row):
    """
    Extract only the new fields (friend_des_words and first_date) from CSV row
    
    Args:
        row (dict): CSV row data
        
    Returns:
        dict: Data containing only the new fields
    """
    new_fields_data = {}
    
    # Extract friend_des_words field
    if 'friend_des_words' in row and row['friend_des_words'] and row['friend_des_words'] != '':
        new_fields_data['friend_des_words'] = row['friend_des_words']
    
    # Extract first_date field
    if 'first_date' in row and row['first_date'] and row['first_date'] != '':
        new_fields_data['first_date'] = row['first_date']
    
    return new_fields_data

def update_profile_new_fields(profile_id, new_fields_data):
    """
    Update user profile with only the new fields data
    """
    url = f"{BASE_URL}/items/UserProfile/{profile_id}"
    
    try:
        response = requests.patch(url, headers=HEADERS_JSON, json=new_fields_data)
        response.raise_for_status()
        
        print(f"✅ Profile {profile_id} updated successfully with new fields")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Error updating profile {profile_id}: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"Response: {e.response.text}")
        return False

def update_profile_from_csv_row(csv_row):
    """
    Update user profile with new fields from CSV row
    Returns: (success, skip_reason)
    """
    sample_id = csv_row['id']
    first_name = csv_row['first_name']
    last_name = csv_row['last_name']
    
    print(f"\n🔄 Updating {first_name} {last_name} (ID: {sample_id})")
    
    # Step 1: Get user profile
    profile = get_user_profile_by_sample_id(sample_id)
    if not profile:
        return False, "Profile not found"
    
    profile_id = profile['id']
    
    # Step 2: Extract new fields data from CSV
    new_fields_data = extract_new_fields_data(csv_row)
    
    if not new_fields_data:
        print(f"⚠️  No new fields data found for {first_name} {last_name}")
        return True, "No new fields to update"
    
    # Show what will be updated
    print(f"📝 New fields to update: {list(new_fields_data.keys())}")
    for field, value in new_fields_data.items():
        print(f"   {field}: {value}")
    
    # Step 3: Update profile with new fields data
    print(f"🔄 Updating profile {profile_id} with new fields")
    success = update_profile_new_fields(profile_id, new_fields_data)
    
    if success:
        print(f"✅ Successfully updated {first_name} {last_name}")
        return True, "Success"
    else:
        return False, "Profile update failed"

def main():
    parser = argparse.ArgumentParser(description='Update user profiles with new fields from CSV')
    parser.add_argument('--start', type=int, default=0, help='Start from record number (0-based)')
    parser.add_argument('--limit', type=int, help='Limit number of records to process')
    parser.add_argument('--csv', default=DEFAULT_CSV_PATH, help='Path to CSV file')
    parser.add_argument('--test', action='store_true', help='Test mode - process only first record')
    
    args = parser.parse_args()
    
    # Test mode overrides
    if args.test:
        args.start = 0
        args.limit = 1
        print("🧪 Running in TEST MODE - processing only first record")
    
    csv_path = args.csv
    if not os.path.exists(csv_path):
        print(f"❌ CSV file not found: {csv_path}")
        sys.exit(1)
    
    print(f"🔄 Updating profiles with new fields from CSV...")
    print(f"📊 Reading CSV file: {csv_path}")
    print(f"🌐 API Base URL: {BASE_URL}")
    print(f"🎯 Target fields: friend_des_words, first_date")
    
    # Read CSV file
    records = []
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        records = list(reader)
    
    total_records = len(records)
    print(f"📋 Total records in CSV: {total_records}")
    
    # Apply start and limit
    start_idx = args.start
    if args.limit:
        end_idx = min(start_idx + args.limit, total_records)
    else:
        end_idx = total_records
    
    records_to_process = records[start_idx:end_idx]
    print(f"🎯 Processing records {start_idx} to {end_idx-1} ({len(records_to_process)} records)")
    
    # Process records
    success_count = 0
    error_count = 0
    skip_count = 0
    
    for i, record in enumerate(records_to_process, start=start_idx):
        print(f"\n{'='*60}")
        print(f"📝 Record {i+1}/{total_records}")
        
        success, reason = update_profile_from_csv_row(record)
        
        if success:
            if reason == "No new fields to update":
                skip_count += 1
            else:
                success_count += 1
        else:
            error_count += 1
            print(f"❌ Failed: {reason}")
        
        # Rate limiting - 0.5 second delay
        if i < end_idx - 1:  # Don't delay after the last record
            print("⏱️  Waiting 0.5 seconds...")
            time.sleep(0.5)
    
    # Final summary
    print(f"\n{'='*60}")
    print("📊 FINAL SUMMARY")
    print(f"✅ Successfully updated: {success_count}")
    print(f"⚠️  Skipped (no new fields): {skip_count}")
    print(f"❌ Failed: {error_count}")
    print(f"📋 Total processed: {len(records_to_process)}")
    
    if records_to_process:
        success_rate = (success_count / len(records_to_process)) * 100
        print(f"📈 Success rate: {success_rate:.1f}%")

if __name__ == "__main__":
    main()
