#!/usr/bin/env python3
"""
Avatar Re-upload <PERSON><PERSON>t

This script re-uploads avatar images with correct MIME types for user profiles.
It will replace existing avatars that were uploaded with incorrect content types.

Usage:
    python3 reupload_avatars.py [--start N] [--limit N] [--csv path] [--test]
"""

import argparse
import csv
import json
import mimetypes
import os
import requests
import sys
import time
from pathlib import Path

# Configuration
BASE_URL = "https://cms.copula.site"
SESSION_TOKEN = "pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO"
DEFAULT_CSV_PATH = "new_data.csv"
IMAGE_FOLDER = "/Users/<USER>/Documents/hailstone/copula_backend/files/face_photos_2k"

# Headers for API requests
HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'Cookie': f'directus_session_token={SESSION_TOKEN}'
}

HEADERS_JSON = {
    'accept': 'application/json, text/plain, */*',
    'content-type': 'application/json',
    'Cookie': f'directus_session_token={SESSION_TOKEN}'
}

def get_user_profile_by_sample_id(sample_id):
    """
    Retrieve user profile by sample_id using the filter API
    """
    url = f"{BASE_URL}/items/UserProfile"
    params = {
        'filter[sample_id][_eq]': sample_id
    }
    
    try:
        response = requests.get(url, headers=HEADERS, params=params)
        response.raise_for_status()
        
        data = response.json()
        if data.get('data') and len(data['data']) > 0:
            return data['data'][0]
        else:
            print(f"❌ No profile found for sample_id: {sample_id}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error retrieving profile for sample_id {sample_id}: {e}")
        return None

def upload_image_file_with_correct_type(file_path):
    """
    Upload an image file to the CMS with correct MIME type and return the image ID
    """
    url = f"{BASE_URL}/files"
    
    if not os.path.exists(file_path):
        print(f"❌ Image file not found: {file_path}")
        return None
    
    # Get the correct MIME type for the image file
    mime_type, _ = mimetypes.guess_type(file_path)
    if not mime_type or not mime_type.startswith('image/'):
        # Default to image/png if we can't determine the type
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext == '.png':
            mime_type = 'image/png'
        elif file_ext in ['.jpg', '.jpeg']:
            mime_type = 'image/jpeg'
        elif file_ext == '.gif':
            mime_type = 'image/gif'
        elif file_ext == '.webp':
            mime_type = 'image/webp'
        else:
            mime_type = 'image/png'  # Default fallback
    
    try:
        with open(file_path, 'rb') as f:
            # Specify the correct content type for the image
            files = {'file': (os.path.basename(file_path), f, mime_type)}
            headers = {'Cookie': f'directus_session_token={SESSION_TOKEN}'}
            response = requests.post(url, headers=headers, files=files)
            response.raise_for_status()
            
            data = response.json()
            if data.get('data') and data['data'].get('id'):
                image_id = data['data']['id']
                print(f"✅ Image uploaded successfully: {image_id} (type: {mime_type})")
                return image_id
            else:
                print(f"❌ Failed to upload image: {file_path}")
                return None
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Error uploading image {file_path}: {e}")
        return None

def delete_old_image(image_id):
    """
    Delete the old image file from CMS
    """
    url = f"{BASE_URL}/files/{image_id}"
    
    try:
        response = requests.delete(url, headers=HEADERS)
        response.raise_for_status()
        print(f"🗑️  Deleted old image: {image_id}")
        return True
    except requests.exceptions.RequestException as e:
        print(f"⚠️  Could not delete old image {image_id}: {e}")
        return False

def update_profile_avatar(profile_id, image_id):
    """
    Update user profile with avatar image ID
    """
    url = f"{BASE_URL}/items/UserProfile/{profile_id}"
    payload = {"avatar": image_id}
    
    try:
        response = requests.patch(url, headers=HEADERS_JSON, json=payload)
        response.raise_for_status()
        
        print(f"✅ Profile {profile_id} updated with new avatar {image_id}")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Error updating profile {profile_id} with avatar: {e}")
        return False

def reupload_user_avatar(csv_row):
    """
    Re-upload avatar for a single user with correct MIME type
    Returns: (success, skip_reason)
    """
    sample_id = csv_row['id']
    file_name = csv_row['file_name']
    first_name = csv_row['first_name']
    last_name = csv_row['last_name']
    
    print(f"\n🔄 Re-uploading {first_name} {last_name} (ID: {sample_id})")
    
    # Step 1: Get user profile
    profile = get_user_profile_by_sample_id(sample_id)
    if not profile:
        return False, "Profile not found"
    
    profile_id = profile['id']
    old_avatar = profile.get('avatar')
    
    # Step 2: Check if image file exists
    image_path = os.path.join(IMAGE_FOLDER, file_name)
    if not os.path.exists(image_path):
        print(f"❌ Image file not found: {image_path}")
        return False, "Image file not found"
    
    # Step 3: Upload new image with correct MIME type
    print(f"📤 Re-uploading image with correct MIME type: {file_name}")
    new_image_id = upload_image_file_with_correct_type(image_path)
    if not new_image_id:
        return False, "Image upload failed"
    
    # Step 4: Update profile with new avatar
    print(f"🔄 Updating profile {profile_id} with new avatar {new_image_id}")
    success = update_profile_avatar(profile_id, new_image_id)
    
    if success:
        # Step 5: Delete old image if it exists and update was successful
        if old_avatar and old_avatar != new_image_id:
            delete_old_image(old_avatar)
        
        print(f"✅ Successfully re-uploaded {first_name} {last_name}")
        return True, "Success"
    else:
        return False, "Profile update failed"

def main():
    parser = argparse.ArgumentParser(description='Re-upload avatar images with correct MIME types')
    parser.add_argument('--start', type=int, default=0, help='Start from record number (0-based)')
    parser.add_argument('--limit', type=int, help='Limit number of records to process')
    parser.add_argument('--csv', default=DEFAULT_CSV_PATH, help='Path to CSV file')
    parser.add_argument('--test', action='store_true', help='Test mode - process only first record')
    
    args = parser.parse_args()
    
    # Test mode overrides
    if args.test:
        args.start = 0
        args.limit = 1
        print("🧪 Running in TEST MODE - processing only first record")
    
    csv_path = args.csv
    if not os.path.exists(csv_path):
        print(f"❌ CSV file not found: {csv_path}")
        sys.exit(1)
    
    print(f"🔄 Re-uploading avatars with correct MIME types...")
    print(f"📊 Reading CSV file: {csv_path}")
    print(f"📁 Image folder: {IMAGE_FOLDER}")
    print(f"🌐 API Base URL: {BASE_URL}")
    
    # Read CSV file
    records = []
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        records = list(reader)
    
    total_records = len(records)
    print(f"📋 Total records in CSV: {total_records}")
    
    # Apply start and limit
    start_idx = args.start
    if args.limit:
        end_idx = min(start_idx + args.limit, total_records)
    else:
        end_idx = total_records
    
    records_to_process = records[start_idx:end_idx]
    print(f"🎯 Processing records {start_idx} to {end_idx-1} ({len(records_to_process)} records)")
    
    # Process records
    success_count = 0
    error_count = 0
    
    for i, record in enumerate(records_to_process, start=start_idx):
        print(f"\n{'='*60}")
        print(f"📝 Record {i+1}/{total_records}")
        
        success, reason = reupload_user_avatar(record)
        
        if success:
            success_count += 1
        else:
            error_count += 1
            print(f"❌ Failed: {reason}")
        
        # Rate limiting - 0.5 second delay
        if i < end_idx - 1:  # Don't delay after the last record
            print("⏱️  Waiting 0.5 seconds...")
            time.sleep(0.5)
    
    # Final summary
    print(f"\n{'='*60}")
    print("📊 FINAL SUMMARY")
    print(f"✅ Successfully re-uploaded: {success_count}")
    print(f"❌ Failed: {error_count}")
    print(f"📋 Total processed: {len(records_to_process)}")
    
    success_rate = (success_count / len(records_to_process)) * 100 if records_to_process else 0
    print(f"📈 Success rate: {success_rate:.1f}%")

if __name__ == "__main__":
    main()
