# -*- coding: utf-8 -*-
"""Copula: Text generation_Gemini.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1Adcyf6zLkbwn-qkdJtrzCR-7AVV03PU3

## Fact sheets

### Introducing
"""

fact_sheet_peronality_ideal_type = """
OPENNESS
Definition:
Individual differences in the tendency to be open to new aesthetic, cultural, or intellectual experiences.

If Openness score == 0, sample completion: A grounded and reliable homebody.
Keywords: Practical, realistic, consistent, familiar

If Openness score == 1, sample completion: A curious and imaginative soul.
Keywords: Imaginative, curious, creative, unconventional, explorer

---

CONSCIENTIOUSNESS
Definition:
Individual differences in the tendency to be organized, responsible, and hardworking,
construed as one end of a dimension of individual differences (conscientiousness vs. lack of direction)

If Conscientiousness score == 0, sample completion: Is a free spirit who go with the flow.
Keywords: Spontaneous, unstructured, flexible, adaptable

If Conscientiousness score == 1, sample completion: Is an organized and responsible “Architect"
Keywords: Organized, disciplined, thorough, prepared

EXTRAVERSION
Definition:
Characterized by an orientation of one’s interests and energies toward the outer world of people and things rather than the inner world of subjective experience.
Extraversion is a broad personality trait and, like introversion, exists on a continuum of attitudes and behaviors.
Extraverts are relatively outgoing, gregarious, sociable, and openly expressive.

If Extraversion score == 0, sample completion: Is a reflective and thoughtful listener.
Keywords: Self-sufficient, reserved, introspective, quiet, solitary

If Extraversion score == 1, sample completion: Is a cheerful and energetic social butterfly.
Keywords: Outgoing, sociable, enthusiastic, expressive, lively

AGREEABLENESS
Definition:
Individual differences in the tendency to act in a cooperative, unselfish manner, construed as one end of a dimension of individual differences
(agreeableness vs. disagreeableness)

If Agreeableness score == 0, sample completion: Is a honest and straightforward soul without filters
Keywords: Assertive, self-reliant, direct, questioning

If Agreeableness score == 1, sample completion: Is a compassionate and peace-loving partner.
Keywords: Friendly, cooperative, gentle, altruistic, polite

NEUROTICISM
Definition:
Characterized by a chronic level of emotional instability and proneness to psychological distress.
The state of being neurotic or a proneness to neurosis.
A mild condition of neurosis.

If Neuroticism score == 0, sample completion: Is a calm and easy-going rock.
Keywords: Resilient, composed, unreactive, stable, Even-temered

If Neuroticism score of 1, sample completion: Is a tender and sentimental spirit.
Keywords: Sensitive, self-conscious, expressive, attuned, passionate
"""

fact_sheet_values_ideal_type = """
TRADITION
Definition:
Defining goal: respect, commitment, and acceptance of the customs and ideas that one's culture or religion provides.
Groups everywhere develop practices, symbols, ideas, and beliefs that represent their shared experience and fate. These become sanctioned as valued group customs and traditions. They symbolize the group's solidarity, express its unique worth, and contribute to its survival (Durkheim, 1912/1954; Parsons, 1951). They often take the form of religious rites, beliefs, and norms of behavior. (respect for tradition, humble, devout, accepting my portion in life) [moderate, spiritual life]
Tradition values demand responsiveness to immutable expectations from the past.
Tradition entails subordination to more abstract objects—religious and cultural customs and ideas.

If tradition score == 0, sample completion: "A new school romantic"
Keywords: Morden, Progress, Dynamic, Receptive, self-defined, open-minded

If tradition score == 1, sample completion: "A classic romantic"
Keywords: Traditional, conventional, respectful,  "old soul", grounded

CONFORMITY
Definition
- Defining goal: restraint of actions, inclinations, and impulses likely to upset or harm others and violate social expectations or norms.
- Conformity values derive from the requirement that individuals inhibit inclinations that might disrupt and undermine smooth interaction and group functioning. As I define them, conformity values emphasize self-restraint in everyday interaction, usually with close others. (obedient, self-discipline, politeness, honoring parents and elders) [loyal, responsible]
- Conformity values exhort responsiveness to current, possibly changing expectations.
- Conformity entails subordination to persons with whom one frequently interacts—parents, teachers, and bosses.

If conformity score == 0, sample completion: "A free thinker dances to their own beat"
Keywords: Self-Direction, autonomy, Freedom, Offbeat, individualistic

If conformity score == 1, sample completion: "A thoughtful team player"
Keywords: Obedient, Compliant, Rule-follower, Mainstream, respectful
"""

"""### Matching"""

fact_sheet_peronality_matching = """
OPENNESS
Definition:
Individual differences in the tendency to be open to new aesthetic, cultural, or intellectual experiences.

If Openness score == 0, sample completion:
- Find happiness in simple, reliable pleasures in daily life
- “Staying in is the new going out.”
Keywords: Practical, realistic, consistent, familiar

If Openness score == 1, sample completion:
- Always up for a spontaneous ideas
- Ready to explore the world with you
Keywords: Imaginative, curious, creative, unconventional, explorer

---

CONSCIENTIOUSNESS
Definition:
Individual differences in the tendency to be organized, responsible, and hardworking,
construed as one end of a dimension of individual differences (conscientiousness vs. lack of direction)

If Conscientiousness score == 0, sample completion:
- “The best plans are no plans at all!”
- Bring you fun, spontaneous ideas and enjoy whatever today brings
Keywords: Spontaneous, unstructured, flexible, adaptable

If Conscientiousness score == 1, sample completion:
- Remember anniversaries and follow through on plans // Never forgets an anniversary and probably reads the menu beforehand
- Think ahead for your shared future
Keywords: Organized, disciplined, thorough, prepared

---

EXTRAVERSION
Definition:
Characterized by an orientation of one’s interests and energies toward the outer world of people and things rather than the inner world of subjective experience.
Extraversion is a broad personality trait and, like introversion, exists on a continuum of attitudes and behaviors.
Extraverts are relatively outgoing, gregarious, sociable, and openly expressive.

If Extraversion score == 0, sample completion:
- Pick a 1:1 deep chat over a loud party any day
- Loves peaceful moment and low-drama relationship
Keywords: Self-sufficient, reserved, introspective, quiet, solitary

If Extraversion score == 1, sample completion:
- Usually the one telling the funniest story at the party
- Can’t wait to turn every day into a new adventure with you
Keywords: Outgoing, sociable, enthusiastic, expressive, lively

---

AGREEABLENESS
Definition:
Individual differences in the tendency to act in a cooperative, unselfish manner, construed as one end of a dimension of individual differences
(agreeableness vs. disagreeableness)

If Agreeableness score == 0, sample completion
- “What you see is what you get”
- No-games approach to dating but get ready for friendly debates
Keywords: Assertive, self-reliant, direct, questioning

If Agreeableness score == 1, sample completion
- A pro at seeing things from your perspective
- Your most corporate partner
Keywords: Friendly, cooperative, gentle, altruistic, polite

---

NEUROTICISM
Definition:
Characterized by a chronic level of emotional instability and proneness to psychological distress.
The state of being neurotic or a proneness to neurosis.
A mild condition of neurosis.

If Neuroticism score == 0, sample completion:
- Not much shakes this cool and stable heart
- Signals steady and relaxed vibe all the time
Keywords: Resilient, composed, unreactive, stable, Even-temered

If Neuroticism score of 1, sample completion:
- Feels all the feels intensely
- Ready for deep exchange of emotions
Keywords: Sensitive, self-conscious, expressive, attuned, passionate
"""

fact_sheet_values_matching = """
TRADITION
Definition:
Defining goal: respect, commitment, and acceptance of the customs and ideas that one's culture or religion provides.
Groups everywhere develop practices, symbols, ideas, and beliefs that represent their shared experience and fate. These become sanctioned as valued group customs and traditions. They symbolize the group's solidarity, express its unique worth, and contribute to its survival (Durkheim, 1912/1954; Parsons, 1951). They often take the form of religious rites, beliefs, and norms of behavior. (respect for tradition, humble, devout, accepting my portion in life) [moderate, spiritual life]
Tradition values demand responsiveness to immutable expectations from the past.
Tradition entails subordination to more abstract objects—religious and cultural customs and ideas.

If tradition score == 0, sample completion:
- What feels right for us are more important
- Can’t wait to create our shared customs
Keywords: Morden, Progress, Dynamic, Receptive, self-defined, open-minded

If tradition score == 1, sample completion:
- “Things are timeless for a reason”
- Build a life filled with meaningful rituals and shared history
Keywords: Traditional, conventional, respectful,  "old soul", grounded

---

CONFORMITY
Definition
- Defining goal: restraint of actions, inclinations, and impulses likely to upset or harm others and violate social expectations or norms.
- Conformity values derive from the requirement that individuals inhibit inclinations that might disrupt and undermine smooth interaction and group functioning. As I define them, conformity values emphasize self-restraint in everyday interaction, usually with close others. (obedient, self-discipline, politeness, honoring parents and elders) [loyal, responsible]
- Conformity values exhort responsiveness to current, possibly changing expectations.
- Conformity entails subordination to persons with whom one frequently interacts—parents, teachers, and bosses.

If conformity score == 0, sample completion:
- Why follow “rules” when we can write better ones together?
- It’s a lot more fun to keep our individuality
Keywords: Self-Direction, autonomy, Freedom, Offbeat, individualistic

If conformity score == 1, sample completion:
- A pro at charming your family
- “Always trying to fit in”
Keywords: Obedient, Compliant, Rule-follower, Mainstream, respectful
"""

"""## Gemini API"""

from google.colab import userdata
import json
import google.generativeai as genai

# Initialize Gemini client with API key from environment
gemini_api_key = userdata.get('gemini_api')
if not gemini_api_key:
    raise ValueError("GEMINI_API_KEY environment variable is not set")
genai.configure(api_key=gemini_api_key)

"""### Why it is your ideal type"""

#@title Introducing Ideal type - personality and value

def intro_idea_type_personality_value(synthetic_profile: dict) -> str:
  try:
    synth_personality = synthetic_profile['personality']
    synth_value = synthetic_profile['value_score']
    synth_interest = synthetic_profile['interest']
    synth_gender = synthetic_profile['gender']

    # Initialize shared personality and value arrays
    prompts = (
        f"""
        As a marriage AI assistant, your job is to write a short introduction of the user ideal partner.
        The introduction should include personality {synth_personality} and value {synth_value}.
        Use appropriate pronoun based on the gender {synth_gender}.
        The answer should start with something like this: 'Your type is' then completed by information from technical specifications.
        Using keywords in the technical specifications as your guideline.
        Do not mention score or name of the traits.
        Technical specifications: ```{fact_sheet_peronality_ideal_type}, {fact_sheet_values_ideal_type}``
        Do not mention which score is matching or complementing which other score.
        Do not add or mention name of a person.
        """
    )
    model = genai.GenerativeModel("gemini-1.5-flash")
    response = model.generate_content(prompts)

    return response.text.strip()

  except Exception as e:
      print(f"Error generating explanation with Gemini: {str(e)}")
      return f":thumpdown"

# import json
# with open('/content/drive/MyDrive/Copula PoC/category_to_interests.json', 'r') as f:
#     category_to_interests = json.load(f)

#@title Introducing ideal type - interest category

import json
with open('/content/drive/MyDrive/Copula PoC/category_to_interests.json', 'r') as f:
    category_to_interests = json.load(f)

def category_look_up(synth_profile, category_to_interests):
  try:
    synth_interest = synth_profile['interest']
    category_list = []
    for interest in synth_interest:
      for category, interests in category_to_interests.items():
        if interest in interests:
          category_list.append(category)
          break
    return category_list
  except: print('Cant find category')

def intro_idea_type_interest_category(user_profile,synth_profile, category_to_interests) -> str:
  synth_gender = synth_profile['gender']
  synth_interest = synth_profile['interest']
  user_interst   = user_profile['interest']
  try:
    category_list = category_look_up(synth_profile,category_to_interests)

    # Initialize interest category
    prompts = (
        f"""
        As a marriage AI assistant, your job is to write a short introduction of the user ideal partner based on the idea that similar hobbies help people bond together.
        The introduction should based on the interst category {category_list}.
        Try to combine those categories together to provide one solid answer.
        Use appropriate pronoun based on the gender {synth_gender}.
        Do not add or mention name of a person.
        Sample answer: as you love explore the world go to concert and creating art will have greate time together.
        limit to 1 or 2 sentences
        """
    )
    model = genai.GenerativeModel("gemini-1.5-flash")
    response = model.generate_content(prompts)

    return response.text.strip()

  except Exception as e:
      print(f"Error generating explanation with Gemini: {str(e)}")
      return f":thumpdown"

"""### Matching"""

#@title Your AI-powered Spark
# this is supposed to be AI summary of the "matched" tags - randomly choose if there is more than 3

import random

def value_map_tag(value_traits: dict):
  value_mapping = {
    'Tradition': {1: 'traditional', 0: 'progressive'},
    'Conformity': {1: 'compliant', 0: 'autonomous'}
    }
  return [
      value_mapping[value][score]
      for value, score in value_traits.items()
      if value in value_mapping and score in value_mapping[value]
  ]


def map_personality_tag(personality_scores: dict) -> dict:

  personality_mapping = {
    'Openness': {1: 'imaginative', 0: 'conventional'},
    'Conscientiousness': {1: 'organised', 0: 'spontaneous'},
    'Extraversion': {1: 'outgoing', 0: 'reserved'},
    'Agreeableness': {1: 'warm', 0: 'independent'},
    'Neuroticism': {1: 'sensitive', 0: 'calm'}
  }
  return [
      personality_mapping[trait][score]
      for trait, score in personality_scores.items()
      if trait in personality_mapping and score in personality_mapping[trait]
  ]


def ai_spark_3_matched_tags(match: dict, synthetic_profile: dict) -> str:
    """
    Generate a short explanation using Gemini API.

    Args:
        match: Match profile dictionary
        synthetic_profile: Synthetic profile dictionary

    Returns:
        Generated explanation string based on  the "matched" tags - randomly choose if there is more than 3
    """
    try:
        match_name = match.get('name')
        match_personality = match['personality']
        match_value = match['value_score']
        match_interest = match['interest']
        match_gender = match['gender']

        synthetic_name = "you"
        synth_personality = synthetic_profile['personality']
        synth_value = synthetic_profile['value_score']
        synth_interest = synthetic_profile['interest']

        shared_personality = {}
        personality_traits = ["Openness", "Conscientiousness", "Extraversion", "Agreeableness", "Neuroticism"]
        # Iterate through the personality traits in synth_profile
        for trait, score in synth_personality.items():
            # Check if the trait exists in match_profile and the scores match
            if trait in match_personality and match_personality[trait] == score:
                shared_personality[trait] = score

        # Shared value
        shared_value = {}
        value_traits = ["Tradition", "Conformity"]
        for i in range(len(synth_value)):
          if synth_value[i] == match_value[i]:
            shared_value[value_traits[i]] = synth_value[i]

        # Shared interest
        shared_interest = [interest for interest in synth_interest if interest in match_interest]

        combined_tags = value_map_tag(shared_value) + map_personality_tag(shared_personality) + shared_interest
        selected_tags = random.sample(combined_tags, min(3, len(combined_tags)))

        system_and_user_prompt = (
          f"""
          Your task as a marriage consultant is to help explain why you suggest a candidate that is suitable with the user
          using keywords listed in {combined_tags}. Using {match_gender} for suitable pronoun.
          Limit to 1 or 2 sentences
          Write 1 sentence with no more than 30 words.
          Do not use pronounce like 'I' or 'We'. Use you, or your for more interactive expression
          Start the answer with 'Just as your type'
          Sample answer is 'Just as your type, she is an introvert who thinks deeply and loves exploring the world - traveling and camping.'
          """
        )

        model = genai.GenerativeModel("gemini-1.5-flash")
        response = model.generate_content(system_and_user_prompt)

        print(f"Shared tag: {selected_tags}\n")
        return response.text.strip()

    except Exception as e:
        print(f"Error generating explanation with Gemini: {str(e)}")
        return f":thumpdown"

#@title  How AI see Match's Vibe
## Matching description

def match_explain_gemini(match: dict, synthetic_profile: dict) -> str:
    """
    Generate a short explanation using Gemini API (simplified version).

    Args:
        match: Match profile dictionary
        synthetic_profile: Synthetic profile dictionary

    Returns:
        Generated explanation string
    """
    try:
        match_name = match.get('name')
        match_personality = match['personality']
        match_value = match['value_score']
        match_interest = match['interest']

        synthetic_name = "you"
        synth_personality = synthetic_profile['personality']
        synth_value = synthetic_profile['value_score']
        synth_interest = synthetic_profile['interest']

        shared_personality = {}
        personality_traits = ["Openness", "Conscientiousness", "Extraversion", "Agreeableness", "Neuroticism"]
        # Iterate through the personality traits in synth_profile
        for trait, score in synth_personality.items():
            # Check if the trait exists in match_profile and the scores match
            if trait in match_personality and match_personality[trait] == score:
                shared_personality[trait] = score

        # Shared value
        shared_value = {}
        value_traits = ["Tradition", "Conformity"]
        for i in range(len(synth_value)):
          if synth_value[i] == match_value[i]:
            shared_value[value_traits[i]] = synth_value[i]

        # Shared interest
        shared_interest = [interest for interest in synth_interest if interest in match_interest]

        system_and_user_prompt = (
          f"""
          Shared_personality: {shared_personality}
          Shared_value: {shared_value}
          Your task as a marriage consultant is to help explain why you suggest a candidate that is suitable with the user using on shared personality, values, and interests.
          Limit to 4-5 sentences
          Personality and value scores are labeled 0 or 1 with 1 means high and 0 means low.
          When mention the match, use match_name {match_name}. When mention the user, use 'You'.
          Write a short explaination based on the information and keywords provided in the technical specifications delimited by
          triple backticks. The answer should start with something like this
          'When {match_name} is in a relationship, {match_name} tends to'
          then completed by information from technical specifications and shared interests.
          Using keywords in the technical specifications as your guideline.
          Do not mention score or name of the traits.
          Technical specifications: ```{fact_sheet_peronality_matching}, {fact_sheet_values_matching}, {shared_interest}```
          Shared interest: ```{shared_interest}```

          """
        )

        model = genai.GenerativeModel("gemini-1.5-flash")
        response = model.generate_content(system_and_user_prompt)

        print(f"Shared Personality: {shared_personality}\nShared Value: {shared_value}\nShared Interest: {shared_interest}\n")
        return response.text.strip()

    except Exception as e:
        print(f"Error generating explanation with Gemini: {str(e)}")
        return f":thumpdown"

"""### Suggest first date"""

# Suggest first date
def first_date_idea_gemini(match: dict, synthetic_profile: dict) -> str:
    """
    Generate a short explanation using Gemini API (simplified version).

    Args:
        match: Match profile dictionary
        synthetic_profile: Synthetic profile dictionary

    Returns:
        Generated explanation string
    """
    try:
        match_name = match.get('name')
        match_interest = match['interest']

        synthetic_name = "you"
        synth_interest = synthetic_profile['interest']

        # Shared interest
        shared_interest = [interest for interest in synth_interest if interest in match_interest]

        system_and_user_prompt = (
          f"""
          Your task as a marriage consultant suggests a first date idea based on the {shared_interest} between the user and the candidate.
          Do not use pronounce like 'I' or 'We'. Use you, or your for more interactive expression
          Start the answer with 'The first date could be'
          Limit to 2 to 3 sentences.
          Sample answer: 'She is the type who’ll suggest a spontaneous road trip, but also bring snacks for the journey. You’ll have lots of fun in the nature!'
          """
        )

        model = genai.GenerativeModel("gemini-1.5-flash")
        response = model.generate_content(system_and_user_prompt)

        print(f"Shared Interest: {shared_interest}\n")
        return response.text.strip()

    except Exception as e:
        print(f"Error generating explanation with Gemini: {str(e)}")
        return f":thumpdown"

"""### Calling all function"""

import re
import textwrap

user_profile = {
    'name': 'Hana',
    'personality': {
        'Openness': 1,
        'Conscientiousness': 0,
        'Extraversion': 1,
        'Agreeableness': 0,
        'Neuroticism': 1
    },
    'gender' : 'female',
    'value_score': [1,1],
    'interest': ['Jiu-Jitsu', 'Video Games', 'Pizza']
}

synth_profile = {
    'personality': {
        'Extraversion': 1,
        'Agreeableness': 0,
        'Neuroticism': 1
    },
    'gender' : 'male',
    'value_score': [1,1],
    'interest': ['Running', '3D Printing & Modeling', 'Wine tasting']
}
match_profile = {
    'name': 'John',
    'personality': {
        'Openness': 1,
        'Conscientiousness': 0,
        'Extraversion': 1,
        'Agreeableness': 0,
        'Neuroticism': 1
    },
    'gender' : 'male',
    'value_score': [1,0],
    'interest': ['Swimming', 'Traveling', 'Wine tasting']
}
# Introducing ideal types
print('Introducing ideal type based on personality and value:')
explanation_pv = intro_idea_type_personality_value(synth_profile)
sentences = re.split(r'(?<=[.!?])\s+', explanation_pv.strip())
for sentence in sentences:
    wrapped_sentence = textwrap.fill(sentence, width=100) # Adjust width
    print(wrapped_sentence)

print('---\n')
print('Introducing ideal type based on interest category:')
explanation_interests = intro_idea_type_interest_category(user_profile,synth_profile, category_to_interests)
sentences = re.split(r'(?<=[.!?])\s+', explanation_interests.strip())
for sentence in sentences:
    wrapped_sentence = textwrap.fill(sentence, width=100) # Adjust width
    print(wrapped_sentence)

print('---\n')
print('Your AI-powered Spark:')
explanation_interests = ai_spark_3_matched_tags(match_profile, synth_profile)
sentences = re.split(r'(?<=[.!?])\s+', explanation_interests.strip())
for sentence in sentences:
    wrapped_sentence = textwrap.fill(sentence, width=100) # Adjust width
    print(wrapped_sentence)

print('---\n')
print(f"How AI see {match_profile['name']}'s vibe ")
match_desciption = match_explain_gemini(match_profile, synth_profile)
sentences = re.split(r'(?<=[.!?])\s+', match_desciption.strip())
for sentence in sentences:
    wrapped_sentence = textwrap.fill(sentence, width=100) # Adjust width
    print(wrapped_sentence)

print('---\n')
print('First Date Idea:')
fist_date = first_date_idea_gemini(match_profile, synth_profile)
sentences = re.split(r'(?<=[.!?])\s+', fist_date.strip())
for sentence in sentences:
    wrapped_sentence = textwrap.fill(sentence, width=100) # Adjust width
    print(wrapped_sentence)