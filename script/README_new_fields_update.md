# New Fields Update Scripts

This directory contains scripts to update user profiles with the new `friend_des_words` and `first_date` fields from the updated `new_data.csv`.

## Scripts

### 1. `update_new_fields_from_csv.py`
Main script to update profiles with the new fields.

**Features:**
- Updates only the `friend_des_words` and `first_date` fields
- Leaves all other profile data unchanged
- Matches profiles using `sample_id` from CSV `id` field
- Includes rate limiting (0.5 second delay between requests)
- Provides detailed progress reporting

**Usage:**
```bash
# Test mode - update only the first record
python3 update_new_fields_from_csv.py --test

# Update all records
python3 update_new_fields_from_csv.py

# Update records starting from record 100, limit to 50 records
python3 update_new_fields_from_csv.py --start 100 --limit 50

# Use a different CSV file
python3 update_new_fields_from_csv.py --csv /path/to/other_file.csv
```

**Parameters:**
- `--start N`: Start from record number N (0-based, default: 0)
- `--limit N`: Limit number of records to process
- `--csv path`: Path to CSV file (default: new_data.csv)
- `--test`: Test mode - process only first record

### 2. `verify_new_fields.py`
Verification script to check if the new fields were properly updated.

**Usage:**
```bash
# Check the default sample_id (11462)
python3 verify_new_fields.py

# Check a specific sample_id
python3 verify_new_fields.py 12327
```

## CSV Structure

The script expects the CSV to have these columns:
- `id`: Maps to `sample_id` in the profile
- `friend_des_words`: New field containing friend description words
- `first_date`: New field containing first date suggestions
- `first_name`, `last_name`: Used for logging/identification

## Example Workflow

1. **Test with one record:**
   ```bash
   python3 update_new_fields_from_csv.py --test
   ```

2. **Verify the test worked:**
   ```bash
   python3 verify_new_fields.py 11462
   ```

3. **Update all records:**
   ```bash
   python3 update_new_fields_from_csv.py
   ```

4. **Check progress periodically:**
   The script shows detailed progress including success/failure counts.

## Safety Features

- **Field-specific updates**: Only updates the two new fields, never touches existing data
- **Rate limiting**: 0.5 second delay between API calls to avoid overwhelming the server
- **Error handling**: Continues processing even if individual records fail
- **Detailed logging**: Shows exactly what's being updated for each profile
- **Test mode**: Allows testing with just one record before full run

## Output Example

```
🧪 Running in TEST MODE - processing only first record
🔄 Updating profiles with new fields from CSV...
📊 Reading CSV file: new_data.csv
🌐 API Base URL: https://cms.copula.site
🎯 Target fields: friend_des_words, first_date
📋 Total records in CSV: 2666
🎯 Processing records 0 to 0 (1 records)

============================================================
📝 Record 1/2666

🔄 Updating Maurice Lau (ID: 11462)
📝 New fields to update: ['friend_des_words', 'first_date']
   friend_des_words: ['Reliable', 'Friendly', 'Peaceful']
   first_date: Dinner at a local restaurant – a classic choice offering conversation and a relaxed atmosphere.
🔄 Updating profile 5 with new fields
✅ Profile 5 updated successfully with new fields
✅ Successfully updated Maurice Lau

============================================================
📊 FINAL SUMMARY
✅ Successfully updated: 1
⚠️  Skipped (no new fields): 0
❌ Failed: 0
📋 Total processed: 1
📈 Success rate: 100.0%
```
