### 1. here is the curl to create new user:

curl --location 'https://cms.copula.site/users' \
--header 'accept: application/json, text/plain, */*' \
--header 'content-type: application/json' \
--header 'Cookie: directus_session_token=pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO' \
--data-raw '{"role":"b9a6dffa-062b-4a24-b838-8b93d4a91147","email":"<EMAIL>","password":"123456"}'

when create new user, only change its email (like sample3, sample4, etc), everything else can be the same.

sample response:
{
    "data": {
        "id": "5856ef4e-f850-42fe-8ea7-1aaecaff4258",
        "email": "<EMAIL>",
        "password": "**********",
        "status": "active"
    }
}

we can extract its user_id from data.id to use for next step.
### 2. the curl to read user profile:

curl --location  'https://cms.copula.site/items/UserProfile?filter\[user\]\[_eq\]={USER_ID}' \
--header 'accept: application/json, text/plain, */*' \
--header 'Cookie: directus_session_token=pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO'

which USER_ID is the user_id we got from API 1

sample response:

{
    "data": [
        {
            "id": 4,
            "status": "published",
            "user": "5856ef4e-f850-42fe-8ea7-1aaecaff4258",
            "first_name": null,
            "last_name": null,
            "height": null,
            "marriage_status": null,
            "race": null,
            "age": null,
            "avatar": null,
            "desired_relationship": null,
            "essay": null,
            "smoke": null,
            "pets": null,
            "income": null,
            "education": null,
            "drink": null,
            "body_type": null,
            "gender": null,
            "sample_id": null,
            "city": null,
            "work": null,
            "language": null,
            "religion": null,
            "openness": null,
            "conscientiousness": null,
            "extraversion": null,
            "agreeableness": null,
            "neuroticism": null,
            "traditionalism": null,
            "conformity": null,
            "interests": null,
            "family_plan": null,
            "images": []
        }
    ]
}

From here, we can see the user_profile_id is 4, we will use this id for next step. We also know all the fields that we can update.

### 3. the curl to update user profile:

curl --location --request PATCH 'https://cms.copula.site/items/UserProfile/{USER_PROFILE_ID}' \
--header 'accept: application/json, text/plain, */*' \
--header 'content-type: application/json' \
--header 'Cookie: directus_session_token=pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO' \
--data '{"age":34,"first_name":"My first name","race":"asian"}'

all other fields can be included in the same way.

### now write a python script in this folder, read the file new_data.csv, and for each row, do the following:
1. call API 1 to create new user
2. call API 2 to read user profile
3. call API 3 to update user profile using the data from new_data.csv
notice that:
field "id" in new_data.csv corresponds to field "sample_id" in API 3
field "essay0" in new_data.csv corresponds to field "essay" in API 3
field file_name and image_source in new_data.csv should be ignored.
field height_cm in new_data.csv corresponds to field "height" in API 3

### after finish uploading the user profile, now we upload the image for the user avatar

1. API to upload image: 
curl --location 'https://cms.copula.site/files' \
--header 'accept: application/json, text/plain, */*' \
--header 'Cookie: directus_session_token=pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO' \
--form 'file=@"/Users/<USER>/Documents/hailstone/copula_backend/files/{FILE_NAME}"'

which FILE_NAME is the value from field "file_name" in new_data.csv

sample response:
{
    "data": {
        "id": "3a155714-540d-4bc2-a0cb-7f2b04506fb6",
        "storage": "gcs",
        "filename_disk": "3a155714-540d-4bc2-a0cb-7f2b04506fb6.jpg",
        "filename_download": "image1.jpg",
        "title": "Image1",
        "type": "image/jpeg"
    }
}

in which, the image_id is "3a155714-540d-4bc2-a0cb-7f2b04506fb6", we will use this id for next step.

2. API to update user profile with image:
curl --location --request PATCH 'https://cms.copula.site/items/UserProfile/{USER_PROFILE_ID}' \
--header 'accept: application/json, text/plain, */*' \
--header 'content-type: application/json' \
--header 'Cookie: directus_session_token=pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO' \
--data '{"avatar": "{IMAGE_ID}"}'

in this case, IMAGE_ID is "3a155714-540d-4bc2-a0cb-7f2b04506fb6"

3. to select a Profile with sample_id, we can use the following API:

curl --location  'https://cms.copula.site/items/UserProfile?filter[sample_id][_eq]=12327' \
--header 'accept: application/json, text/plain, */*' \
--header 'Cookie: directus_session_token=pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO'

sample response:
{
    "data": [
        {
            "id": 6,
            "status": "published",
            "user": "2eb87063-05c3-4cf2-aa79-cfdae8374b41",
            "first_name": "Jeffery",
            "last_name": "Nguyen",
            "height": 173,
            "marriage_status": null,
            "race": "asian",
            "age": 38,
            "avatar": null,
            "desired_relationship": "Long-term relationship",
            "essay": "i'm not ashamed of much, but writing public text on an online dating site makes me pleasantly uncomfortable. i'll try to be as earnest as possible in the noble endeavor of standing naked before the world.  i've lived in san francisco for 15 years, and both love it and find myself frustrated with its deficits. lots of great friends and acquaintances (which increases my apprehension to put anything on this site), but i'm feeling like meeting some new people that aren't just friends of friends. it's okay if you are a friend of a friend too. chances are, if you make it through the complex filtering process of multiple choice questions, lifestyle statistics, photo scanning, and these indulgent blurbs of text without moving quickly on to another search result, you are probably already a cultural peer and at most 2 people removed. at first, i thought i should say as little as possible here to avoid you, but that seems silly.  as far as culture goes, i'm definitely more on the weird side of the spectrum, but i don't exactly wear it on my sleeve. once you get me talking, it will probably become increasingly apparent that while i'd like to think of myself as just like everybody else (and by some definition i certainly am), most people don't see me that way. that's fine with me. most of the people i find myself gravitating towards are pretty weird themselves. you probably are too.",
            "smoke": "no",
            "pets": "has cats",
            "income": "-1",
            "education": "graduated from masters program",
            "drink": "socially",
            "body_type": "thin",
            "gender": "m",
            "sample_id": "12327",
            "city": "san francisco, california",
            "work": null,
            "language": null,
            "religion": null,
            "openness": "4",
            "conscientiousness": "1",
            "extraversion": "9",
            "agreeableness": "8",
            "neuroticism": "10",
            "traditionalism": null,
            "conformity": null,
            "interests": "Puzzles, Aminals, Traveling, Hiking, Acting",
            "family_plan": null,
            "images": []
        }
    ]
}

where USER_PROFILE_ID is 6, we can use this id for updating the avatar in API 2.

### OK, now go through all users, and for each user, we need to check if it has image. If not, we need to upload the image and update the user profile. The image is in the folder /Users/<USER>/Documents/hailstone/copula_backend/files/{FILE_NAME}", in which FILE_NAME is the value from field "file_name" in new_data.csv, and it map with the user via field "id" in new_data.csv and field "sample_id" in user profile.