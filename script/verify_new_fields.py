#!/usr/bin/env python3
"""
Verification Script for New Fields

This script verifies that the new fields (friend_des_words and first_date) 
have been properly updated in the profiles.

Usage:
    python3 verify_new_fields.py [sample_id]
"""

import argparse
import requests
import sys

# Configuration
BASE_URL = "https://cms.copula.site"
SESSION_TOKEN = "pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO"

# Headers for API requests
HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'Cookie': f'directus_session_token={SESSION_TOKEN}'
}

def get_user_profile_by_sample_id(sample_id):
    """
    Retrieve user profile by sample_id using the filter API
    """
    url = f"{BASE_URL}/items/UserProfile"
    params = {
        'filter[sample_id][_eq]': sample_id
    }
    
    try:
        response = requests.get(url, headers=HEADERS, params=params)
        response.raise_for_status()
        
        data = response.json()
        if data.get('data') and len(data['data']) > 0:
            return data['data'][0]
        else:
            print(f"❌ No profile found for sample_id: {sample_id}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error retrieving profile for sample_id {sample_id}: {e}")
        return None

def verify_profile_fields(sample_id):
    """
    Verify that the new fields exist in the profile
    """
    print(f"🔍 Checking profile for sample_id: {sample_id}")
    
    profile = get_user_profile_by_sample_id(sample_id)
    if not profile:
        return False
    
    print(f"✅ Profile found: {profile.get('first_name', 'Unknown')} {profile.get('last_name', 'Unknown')}")
    print(f"📝 Profile ID: {profile.get('id')}")
    
    # Check new fields
    friend_des_words = profile.get('friend_des_words')
    first_date = profile.get('first_date')
    
    print(f"\n📋 New Fields Status:")
    print(f"friend_des_words: {'✅ Present' if friend_des_words else '❌ Missing'}")
    if friend_des_words:
        print(f"   Value: {friend_des_words}")
    
    print(f"first_date: {'✅ Present' if first_date else '❌ Missing'}")
    if first_date:
        print(f"   Value: {first_date}")
    
    return True

def main():
    parser = argparse.ArgumentParser(description='Verify new fields in user profiles')
    parser.add_argument('sample_id', nargs='?', default='11462', help='Sample ID to check (default: 11462)')
    
    args = parser.parse_args()
    
    sample_id = args.sample_id
    
    print(f"🔍 Verifying new fields for sample_id: {sample_id}")
    print(f"🌐 API Base URL: {BASE_URL}")
    
    success = verify_profile_fields(sample_id)
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
