Matching score = Face_embedding*w1 + Personality*w2 + Interest*w3 + Value*w4 + Race*w5

# New Synthetic Profile Logic

## Face embedding (same as before)
Extract the embedding of ideal type (synthetic image) 
Calculate cosine similarity score of the ideal type’s embedding with facial embedding of other candidates
## Personality
Instead of using "high/medium/low", now each Personality trait only has 2 options.
Personality columns: ['openness', 'conscientiousness','extraversion', 'agreeableness', 'neuroticism']
The Big 5 personality traits are represented using binary options for each trait:
Openness: Imaginative vs Conventional
Conscientiousness: Organised vs Spontaneous
Extraversion: Outgoing vs Reserved
Agreeableness: Warm vs Independent
Neuroticism: Sensitive vs Calm
For the ideal type profile, 
The ideal type profile will have 3 personality traits picked randomly from 5 traits of user profile
To calculate the similarity between ideal type personality with personality of other candidate we can:
Count the number of similar personality traits
OR use embedding of those traits and calculate cosine score
## Interest
Each profile has 3 interests
To create the interest of the ideal type (synthetic profile):
Suggest 3 new interests that are in the same category (random pick from the same category, to know which Interest belongs to which category, we can use the pre-defined list of interests and their categories in file "files/category_to_interests.json")
For example: 
The user’s interest are: Board Games (category: Strategic games), Robotics (category: Technology & Gaming), Running (category: Sport & Fitness)
Suggested interest for ideal type are: Card Games(category: Strategic games), Drone Flying (category: Technology & Gaming), Kayaking (category: Sport & Fitness)

To match the interest of the ideal type with the interests of candidates, we can use text embedding and cosine score. 
Vectorize the ideal type’s interests and calculate the cosine score of the vector to the interest vectors of other candidates. (better at matching similar but not totally the same interests)
If user input an interest that is not in the pre-defined list, then we need to categorize that interest to one of the category that we have (referring to Interest Grouping at the end of the doc)
## Values
Value columns: ['traditionalism','conformity']
Two values, each with two options.
Traditionalism: Traditional VS Progressive
Conformity: Compliant VS Autonomous
The ideal type shares the sample values as the user profile.
Count the shared values between the ideal type and candidates.

# New matching score logics
## Combine those score
Normalize all score to be between 0 and 1
Calculate the weighted average of the score:

Matching score = Face_embedding*w1 + Personality*w2 + Interest*w3 + Value*w4 + Race*w5

w1: 0.5
w2: 0.2
w3: 0.1
w4: 0.1
w5: 0.1
